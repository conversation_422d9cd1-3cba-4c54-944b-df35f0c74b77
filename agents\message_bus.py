"""
Message Bus for Multi-Agent Communication
"""
import asyncio
import logging
from typing import Dict, List, Optional, Callable
from collections import defaultdict
import json
from datetime import datetime

from .base_agent import BaseAgent, Message, MessageType

logger = logging.getLogger(__name__)


class MessageBus:
    """Central message bus for agent communication"""
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.message_history: List[Message] = []
        self.subscribers: Dict[MessageType, List[str]] = defaultdict(list)
        self.running = False
        self.message_queue = asyncio.Queue()
        
        # Performance metrics
        self.messages_sent = 0
        self.messages_failed = 0
        
    async def start(self):
        """Start the message bus"""
        self.running = True
        logger.info("Message bus started")
        
        # Start message routing loop
        asyncio.create_task(self._message_routing_loop())
    
    async def stop(self):
        """Stop the message bus"""
        self.running = False
        logger.info("Message bus stopped")
    
    def register_agent(self, agent: BaseAgent):
        """Register an agent with the message bus"""
        self.agents[agent.agent_id] = agent
        # Set the agent's send_message method to use this bus
        agent.send_message = self._create_send_method(agent.agent_id)
        logger.info(f"Agent {agent.name} ({agent.agent_id}) registered")
    
    def unregister_agent(self, agent_id: str):
        """Unregister an agent from the message bus"""
        if agent_id in self.agents:
            del self.agents[agent_id]
            logger.info(f"Agent {agent_id} unregistered")
    
    def _create_send_method(self, sender_id: str):
        """Create a send_message method for an agent"""
        async def send_message(message: Message):
            message.sender_id = sender_id
            await self.route_message(message)
        return send_message
    
    async def route_message(self, message: Message):
        """Route message to appropriate recipient(s)"""
        await self.message_queue.put(message)
    
    async def _message_routing_loop(self):
        """Main message routing loop"""
        while self.running:
            try:
                # Wait for message with timeout
                message = await asyncio.wait_for(
                    self.message_queue.get(),
                    timeout=1.0
                )
                
                await self._process_message_routing(message)
                
            except asyncio.TimeoutError:
                # No message received, continue
                continue
            except Exception as e:
                logger.error(f"Error in message routing: {e}")
                self.messages_failed += 1
    
    async def _process_message_routing(self, message: Message):
        """Process message routing"""
        try:
            # Add to history
            self.message_history.append(message)
            
            # Route to specific recipient
            if message.receiver_id:
                await self._send_to_agent(message, message.receiver_id)
            
            # Broadcast to subscribers
            elif message.receiver_id == "broadcast":
                await self._broadcast_message(message)
            
            # Route to subscribers of this message type
            else:
                subscribers = self.subscribers.get(message.message_type, [])
                for agent_id in subscribers:
                    await self._send_to_agent(message, agent_id)
            
            self.messages_sent += 1
            
        except Exception as e:
            logger.error(f"Error routing message {message.id}: {e}")
            self.messages_failed += 1
    
    async def _send_to_agent(self, message: Message, agent_id: str):
        """Send message to specific agent"""
        agent = self.agents.get(agent_id)
        if agent:
            await agent.receive_message(message)
        else:
            logger.warning(f"Agent {agent_id} not found for message {message.id}")
    
    async def _broadcast_message(self, message: Message):
        """Broadcast message to all agents except sender"""
        for agent_id, agent in self.agents.items():
            if agent_id != message.sender_id:
                await agent.receive_message(message)
    
    def subscribe(self, agent_id: str, message_type: MessageType):
        """Subscribe agent to specific message type"""
        if agent_id not in self.subscribers[message_type]:
            self.subscribers[message_type].append(agent_id)
            logger.info(f"Agent {agent_id} subscribed to {message_type.value}")
    
    def unsubscribe(self, agent_id: str, message_type: MessageType):
        """Unsubscribe agent from specific message type"""
        if agent_id in self.subscribers[message_type]:
            self.subscribers[message_type].remove(agent_id)
            logger.info(f"Agent {agent_id} unsubscribed from {message_type.value}")
    
    async def send_request(self, sender_id: str, receiver_id: str, 
                          content: Dict, timeout: float = 30.0) -> Optional[Message]:
        """Send request and wait for response"""
        request = Message(
            sender_id=sender_id,
            receiver_id=receiver_id,
            message_type=MessageType.REQUEST,
            content=content,
            correlation_id=str(asyncio.current_task())
        )
        
        # Send request
        await self.route_message(request)
        
        # Wait for response
        start_time = datetime.now()
        while (datetime.now() - start_time).total_seconds() < timeout:
            # Check message history for response
            for message in reversed(self.message_history):
                if (message.correlation_id == request.correlation_id and
                    message.sender_id == receiver_id and
                    message.receiver_id == sender_id and
                    message.message_type in [MessageType.RESPONSE, MessageType.ERROR]):
                    return message
            
            await asyncio.sleep(0.1)
        
        # Timeout
        logger.warning(f"Request {request.id} timed out")
        return None
    
    def get_agent_list(self) -> List[Dict]:
        """Get list of registered agents"""
        return [agent.get_info() for agent in self.agents.values()]
    
    def get_message_history(self, limit: int = 100) -> List[Dict]:
        """Get recent message history"""
        recent_messages = self.message_history[-limit:]
        return [msg.to_dict() for msg in recent_messages]
    
    def get_statistics(self) -> Dict:
        """Get message bus statistics"""
        return {
            "agents_count": len(self.agents),
            "messages_sent": self.messages_sent,
            "messages_failed": self.messages_failed,
            "message_history_size": len(self.message_history),
            "subscribers": {
                msg_type.value: len(agents) 
                for msg_type, agents in self.subscribers.items()
            }
        }


# Global message bus instance
message_bus = MessageBus()
