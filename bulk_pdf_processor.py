"""
Processador em Massa de PDFs para o Sistema Multi-Agente
Processa centenas de PDFs científicos e os adiciona ao sistema
"""
import asyncio
import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any
import PyPDF2
import fitz  # pymupdf
from datetime import datetime
import json

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.agent_manager import agent_manager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BulkPDFProcessor:
    """Processador em massa de PDFs científicos"""
    
    def __init__(self, pdf_directory: str):
        self.pdf_directory = Path(pdf_directory)
        self.processed_files = []
        self.failed_files = []
        self.total_chunks = 0
        
    def extract_text_from_pdf(self, pdf_path: Path) -> List[Dict[str, Any]]:
        """Extrair texto de um PDF e dividir em chunks"""
        chunks = []
        
        try:
            # Tentar com PyMuPDF primeiro (melhor qualidade)
            doc = fitz.open(str(pdf_path))
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                
                if text.strip():  # Só processar páginas com texto
                    # Dividir texto em chunks de ~1000 caracteres
                    text_chunks = self._split_text_into_chunks(text, max_length=1000)
                    
                    for i, chunk in enumerate(text_chunks):
                        chunks.append({
                            "filename": pdf_path.name,
                            "text": chunk,
                            "page": page_num + 1,
                            "chunk_id": f"{pdf_path.stem}_page{page_num+1}_chunk{i+1}",
                            "file_path": str(pdf_path),
                            "processed_date": datetime.now().isoformat(),
                            "chunk_index": i,
                            "total_chunks_in_page": len(text_chunks)
                        })
            
            doc.close()
            
        except Exception as e:
            logger.warning(f"PyMuPDF falhou para {pdf_path.name}, tentando PyPDF2: {e}")
            
            # Fallback para PyPDF2
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    
                    for page_num, page in enumerate(pdf_reader.pages):
                        text = page.extract_text()
                        
                        if text.strip():
                            text_chunks = self._split_text_into_chunks(text, max_length=1000)
                            
                            for i, chunk in enumerate(text_chunks):
                                chunks.append({
                                    "filename": pdf_path.name,
                                    "text": chunk,
                                    "page": page_num + 1,
                                    "chunk_id": f"{pdf_path.stem}_page{page_num+1}_chunk{i+1}",
                                    "file_path": str(pdf_path),
                                    "processed_date": datetime.now().isoformat(),
                                    "chunk_index": i,
                                    "total_chunks_in_page": len(text_chunks)
                                })
                                
            except Exception as e2:
                logger.error(f"Falha ao processar {pdf_path.name}: {e2}")
                return []
        
        return chunks
    
    def _split_text_into_chunks(self, text: str, max_length: int = 1000, overlap: int = 100) -> List[str]:
        """Dividir texto em chunks com sobreposição"""
        if len(text) <= max_length:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + max_length
            
            # Tentar quebrar em uma frase completa
            if end < len(text):
                # Procurar por ponto final próximo
                sentence_end = text.rfind('.', start, end)
                if sentence_end > start + max_length // 2:
                    end = sentence_end + 1
                else:
                    # Procurar por espaço
                    space_pos = text.rfind(' ', start, end)
                    if space_pos > start + max_length // 2:
                        end = space_pos
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Próximo chunk com sobreposição
            start = max(end - overlap, start + 1)
            
            if start >= len(text):
                break
        
        return chunks
    
    def find_pdf_files(self) -> List[Path]:
        """Encontrar todos os arquivos PDF no diretório"""
        pdf_files = []
        
        if not self.pdf_directory.exists():
            logger.error(f"Diretório não encontrado: {self.pdf_directory}")
            return []
        
        # Buscar PDFs recursivamente
        for pdf_file in self.pdf_directory.rglob("*.pdf"):
            if pdf_file.is_file():
                pdf_files.append(pdf_file)
        
        logger.info(f"Encontrados {len(pdf_files)} arquivos PDF")
        return pdf_files
    
    async def process_pdfs_batch(self, pdf_files: List[Path], batch_size: int = 10) -> Dict[str, Any]:
        """Processar PDFs em lotes"""
        total_files = len(pdf_files)
        processed_count = 0
        
        logger.info(f"Iniciando processamento de {total_files} PDFs em lotes de {batch_size}")
        
        for i in range(0, total_files, batch_size):
            batch = pdf_files[i:i + batch_size]
            batch_chunks = []
            
            logger.info(f"Processando lote {i//batch_size + 1}/{(total_files-1)//batch_size + 1}")
            
            # Processar cada PDF no lote
            for pdf_path in batch:
                try:
                    logger.info(f"Processando: {pdf_path.name}")
                    chunks = self.extract_text_from_pdf(pdf_path)
                    
                    if chunks:
                        batch_chunks.extend(chunks)
                        self.processed_files.append(str(pdf_path))
                        self.total_chunks += len(chunks)
                        logger.info(f"✅ {pdf_path.name}: {len(chunks)} chunks extraídos")
                    else:
                        self.failed_files.append(str(pdf_path))
                        logger.warning(f"⚠️ {pdf_path.name}: Nenhum texto extraído")
                        
                except Exception as e:
                    self.failed_files.append(str(pdf_path))
                    logger.error(f"❌ Erro processando {pdf_path.name}: {e}")
            
            # Adicionar lote ao sistema
            if batch_chunks:
                try:
                    result = await agent_manager.add_documents(batch_chunks)
                    
                    if "error" in result:
                        logger.error(f"Erro adicionando lote ao sistema: {result['error']}")
                    else:
                        processed_count += len(batch)
                        logger.info(f"✅ Lote adicionado: {result.get('documents_added', 0)} documentos")
                        
                except Exception as e:
                    logger.error(f"Erro adicionando lote: {e}")
            
            # Pequena pausa entre lotes
            await asyncio.sleep(1)
        
        return {
            "total_files": total_files,
            "processed_files": len(self.processed_files),
            "failed_files": len(self.failed_files),
            "total_chunks": self.total_chunks,
            "processed_files_list": self.processed_files,
            "failed_files_list": self.failed_files
        }
    
    def save_processing_report(self, results: Dict[str, Any], output_file: str = "pdf_processing_report.json"):
        """Salvar relatório do processamento"""
        report = {
            "processing_date": datetime.now().isoformat(),
            "pdf_directory": str(self.pdf_directory),
            "results": results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Relatório salvo em: {output_file}")


async def main():
    """Função principal"""
    print("🔄 Processador em Massa de PDFs Científicos")
    print("=" * 50)
    
    # Configurar diretório de PDFs
    pdf_directory = input("Digite o caminho para o diretório com PDFs (ou Enter para usar 'pdfs_cientificos'): ").strip()
    
    if not pdf_directory:
        pdf_directory = "pdfs_cientificos"
    
    # Criar diretório se não existir
    Path(pdf_directory).mkdir(exist_ok=True)
    
    if not Path(pdf_directory).exists() or not any(Path(pdf_directory).rglob("*.pdf")):
        print(f"""
❌ Nenhum PDF encontrado em: {pdf_directory}

📁 Para usar este script:
1. Crie uma pasta chamada 'pdfs_cientificos' (ou outro nome)
2. Coloque seus PDFs científicos nesta pasta
3. Execute novamente este script

Exemplo de estrutura:
pdfs_cientificos/
├── neurocovid_study_1.pdf
├── covid_neurological_2023.pdf
├── anosmia_treatment.pdf
└── ...
        """)
        return
    
    try:
        # Inicializar sistema
        print("🔧 Inicializando sistema multi-agente...")
        await agent_manager.initialize()
        print("✅ Sistema inicializado")
        
        # Criar processador
        processor = BulkPDFProcessor(pdf_directory)
        
        # Encontrar PDFs
        pdf_files = processor.find_pdf_files()
        
        if not pdf_files:
            print(f"❌ Nenhum PDF encontrado em {pdf_directory}")
            return
        
        print(f"📚 Encontrados {len(pdf_files)} PDFs para processar")
        
        # Confirmar processamento
        response = input(f"Deseja processar todos os {len(pdf_files)} PDFs? (s/n): ").strip().lower()
        
        if response != 's':
            print("❌ Processamento cancelado")
            return
        
        # Configurar tamanho do lote
        batch_size = 5  # Processar 5 PDFs por vez
        if len(pdf_files) > 50:
            batch_size = 10  # Lotes maiores para muitos arquivos
        
        print(f"🚀 Iniciando processamento em lotes de {batch_size}...")
        
        # Processar PDFs
        results = await processor.process_pdfs_batch(pdf_files, batch_size)
        
        # Salvar relatório
        processor.save_processing_report(results)
        
        # Exibir resultados
        print("\n" + "=" * 50)
        print("📊 RELATÓRIO DE PROCESSAMENTO")
        print("=" * 50)
        print(f"📁 Total de arquivos: {results['total_files']}")
        print(f"✅ Processados com sucesso: {results['processed_files']}")
        print(f"❌ Falharam: {results['failed_files']}")
        print(f"📄 Total de chunks criados: {results['total_chunks']}")
        print(f"📈 Taxa de sucesso: {results['processed_files']/results['total_files']:.1%}")
        
        if results['failed_files'] > 0:
            print(f"\n⚠️ Arquivos que falharam:")
            for failed_file in results['failed_files_list'][:10]:  # Mostrar apenas os primeiros 10
                print(f"  - {Path(failed_file).name}")
            if len(results['failed_files_list']) > 10:
                print(f"  ... e mais {len(results['failed_files_list']) - 10} arquivos")
        
        print(f"\n🎉 Processamento concluído! Os PDFs estão agora disponíveis para consulta pelos agentes.")
        print(f"📋 Relatório detalhado salvo em: pdf_processing_report.json")
        
        # Finalizar sistema
        await agent_manager.shutdown()
        
    except KeyboardInterrupt:
        print("\n⚠️ Processamento interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante processamento: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
