"""
Test script for the Multi-Agent Medical System
"""
import asyncio
import logging
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.agent_manager import agent_manager
from security.medical_security import compliance_manager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_system_initialization():
    """Test system initialization"""
    print("🔧 Testing System Initialization...")
    
    try:
        # Initialize the multi-agent system
        await agent_manager.initialize()
        print("✅ System initialized successfully")
        
        # Check system status
        status = agent_manager.get_agent_status()
        print(f"📊 System Status: {status['system_running']}")
        print(f"📊 Total Agents: {status['total_agents']}")
        
        return True
        
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        return False


async def test_agent_communication():
    """Test agent communication"""
    print("\n🤖 Testing Agent Communication...")
    
    try:
        # Get agent capabilities
        capabilities = agent_manager.get_agent_capabilities()
        print(f"📋 Available Agents: {list(capabilities.keys())}")
        
        # Test health check
        health = await agent_manager.health_check()
        print(f"🏥 System Health: {health['system_healthy']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent communication test failed: {e}")
        return False


async def test_document_processing():
    """Test document processing"""
    print("\n📄 Testing Document Processing...")
    
    try:
        # Test document addition
        test_documents = [
            {
                "filename": "test_neurocovid.pdf",
                "text": "COVID-19 can cause neurological manifestations including anosmia, ageusia, headache, and cognitive impairment. These symptoms may persist as long COVID.",
                "page": 1,
                "chunk_id": "test_chunk_1"
            },
            {
                "filename": "test_treatment.pdf", 
                "text": "Treatment of neurocovid symptoms includes symptomatic management, rehabilitation therapy, and in severe cases, corticosteroids may be considered.",
                "page": 1,
                "chunk_id": "test_chunk_2"
            }
        ]
        
        result = await agent_manager.add_documents(test_documents)
        
        if "error" in result:
            print(f"❌ Document processing failed: {result['error']}")
            return False
        else:
            print(f"✅ Documents processed successfully: {result.get('documents_added', 0)} documents")
            return True
            
    except Exception as e:
        print(f"❌ Document processing test failed: {e}")
        return False


async def test_query_processing():
    """Test query processing"""
    print("\n🔍 Testing Query Processing...")
    
    try:
        # Test query processing
        test_query = "What are the main neurological symptoms of COVID-19?"
        
        print(f"📝 Processing query: {test_query}")
        
        result = await agent_manager.process_query(
            query=test_query,
            workflow_type="quick_lookup",
            patient_context={"age": 45, "conditions": []}
        )
        
        if "error" in result:
            print(f"❌ Query processing failed: {result['error']}")
            return False
        else:
            print(f"✅ Query processed successfully")
            print(f"📊 Workflow ID: {result.get('workflow_id', 'N/A')}")
            print(f"📊 Status: {result.get('status', 'Unknown')}")
            
            # Print final answer if available
            if "result" in result and "final_answer" in result["result"]:
                answer = result["result"]["final_answer"]
                print(f"💡 Answer preview: {answer[:200]}...")
            
            return True
            
    except Exception as e:
        print(f"❌ Query processing test failed: {e}")
        return False


async def test_security_compliance():
    """Test security and compliance features"""
    print("\n🔒 Testing Security and Compliance...")
    
    try:
        # Test user authentication
        auth_result = compliance_manager.access_control.authenticate_user(
            "test_user", "physician", {}
        )
        
        if auth_result["authenticated"]:
            print("✅ User authentication successful")
            session_token = auth_result["session_token"]
            
            # Test data processing with compliance
            test_data = "Patient presents with anosmia and headache following COVID-19 infection"
            
            compliance_result = compliance_manager.process_medical_data(
                data=test_data,
                user_session=session_token,
                operation="read"
            )
            
            if compliance_result["compliant"]:
                print("✅ Compliance processing successful")
                print(f"📊 Data classification: {compliance_result['classification']['sensitivity_level']}")
                return True
            else:
                print(f"❌ Compliance processing failed: {compliance_result.get('error', 'Unknown error')}")
                return False
        else:
            print("❌ User authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Security compliance test failed: {e}")
        return False


async def test_system_metrics():
    """Test system metrics and monitoring"""
    print("\n📊 Testing System Metrics...")
    
    try:
        # Get system metrics
        metrics = agent_manager.get_system_metrics()
        
        print(f"📈 System Uptime: {metrics['system_metrics']['uptime_seconds']:.1f}s")
        print(f"📈 Total Requests: {metrics['system_metrics']['total_requests']}")
        print(f"📈 Error Rate: {metrics['system_metrics']['error_rate']:.1%}")
        print(f"📈 Active Agents: {metrics['system_metrics']['agents_count']}")
        
        # Get compliance report
        compliance_report = compliance_manager.generate_compliance_report()
        print(f"🔒 Compliance Status: {compliance_report['compliance_status']}")
        print(f"🔒 Success Rate: {compliance_report['metrics']['success_rate']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ System metrics test failed: {e}")
        return False


async def run_all_tests():
    """Run all tests"""
    print("🧪 Starting Multi-Agent Medical System Tests\n")
    
    tests = [
        ("System Initialization", test_system_initialization),
        ("Agent Communication", test_agent_communication),
        ("Document Processing", test_document_processing),
        ("Query Processing", test_query_processing),
        ("Security & Compliance", test_security_compliance),
        ("System Metrics", test_system_metrics)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "="*50)
    print("🧪 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 All tests passed! The multi-agent system is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the logs and fix any issues.")
    
    # Cleanup
    try:
        await agent_manager.shutdown()
        print("\n🔧 System shutdown complete")
    except Exception as e:
        print(f"⚠️ Error during shutdown: {e}")


def main():
    """Main test function"""
    print("Multi-Agent Medical System Test Suite")
    print("====================================")
    
    try:
        # Run tests
        asyncio.run(run_all_tests())
        
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
