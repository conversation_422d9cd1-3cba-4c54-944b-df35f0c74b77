"""
Treatment Recommendation Agent - Specialized in evidence-based treatment suggestions
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
import ollama
import json

from .base_agent import BaseAgent, Message, MessageType
from config.settings import OLLAMA_CONFIG

logger = logging.getLogger(__name__)


class TreatmentRecommendationAgent(BaseAgent):
    """Agent specialized in evidence-based treatment recommendations"""
    
    def __init__(self):
        super().__init__(
            agent_id="treatment_recommendation",
            name="Treatment Recommendation Agent",
            role="Evidence-based treatment and therapy specialist",
            capabilities=[
                "treatment_protocols",
                "medication_recommendations",
                "therapy_suggestions",
                "drug_interactions",
                "guideline_compliance"
            ]
        )
        
        self.ollama_client = None
        self.treatment_knowledge = self._initialize_treatment_knowledge()
        
        # Initialize Ollama client
        asyncio.create_task(self._initialize_ollama())
        
        # Register message handlers
        self.message_handlers[MessageType.REQUEST] = self._handle_request
    
    async def _initialize_ollama(self):
        """Initialize Ollama client"""
        try:
            self.ollama_client = ollama.Client(host=OLLAMA_CONFIG["base_url"])
            self.logger.info("Treatment Recommendation Agent initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing Ollama client: {e}")
    
    def _initialize_treatment_knowledge(self) -> Dict[str, Any]:
        """Initialize treatment knowledge base"""
        return {
            "neurocovid_treatments": {
                "symptomatic": {
                    "headache": ["acetaminophen", "ibuprofen", "topiramate"],
                    "anosmia": ["olfactory_training", "corticosteroids", "vitamin_a"],
                    "fatigue": ["graded_exercise", "cognitive_behavioral_therapy", "sleep_hygiene"],
                    "cognitive_impairment": ["cognitive_rehabilitation", "cholinesterase_inhibitors"],
                    "depression": ["ssri", "psychotherapy", "mindfulness"]
                },
                "neuroprotective": [
                    "corticosteroids", "immunoglobulins", "plasmapheresis",
                    "antioxidants", "neuroprotective_agents"
                ],
                "rehabilitation": [
                    "physical_therapy", "occupational_therapy", "speech_therapy",
                    "cognitive_rehabilitation", "neuropsychological_support"
                ]
            },
            "contraindications": {
                "corticosteroids": ["active_infection", "diabetes_uncontrolled", "psychosis"],
                "nsaids": ["kidney_disease", "heart_failure", "bleeding_disorders"],
                "antidepressants": ["mania", "seizure_disorders", "drug_interactions"]
            },
            "drug_interactions": {
                "warfarin": ["antibiotics", "antifungals", "nsaids"],
                "antiepileptics": ["oral_contraceptives", "warfarin", "antidepressants"],
                "immunosuppressants": ["live_vaccines", "certain_antibiotics"]
            },
            "monitoring_requirements": {
                "corticosteroids": ["blood_glucose", "blood_pressure", "bone_density"],
                "antiepileptics": ["liver_function", "blood_counts", "drug_levels"],
                "immunosuppressants": ["infection_signs", "blood_counts", "organ_function"]
            },
            "evidence_levels": {
                "high": ["corticosteroids_for_encephalitis", "rehabilitation_therapy"],
                "moderate": ["olfactory_training", "cognitive_rehabilitation"],
                "low": ["vitamin_supplements", "alternative_therapies"]
            }
        }
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming treatment request"""
        action = request.get("action", "")
        inputs = request.get("inputs", {})
        
        if action == "suggest_treatments":
            return await self._suggest_treatments(inputs)
        elif action == "check_interactions":
            return await self._check_drug_interactions(inputs)
        elif action == "protocol_recommendations":
            return await self._protocol_recommendations(inputs)
        elif action == "rehabilitation_plan":
            return await self._rehabilitation_plan(inputs)
        else:
            return {"error": f"Unknown action: {action}"}
    
    async def _suggest_treatments(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest evidence-based treatments"""
        try:
            diagnostic_results = inputs.get("diagnostic_analysis_result", {})
            symptoms = diagnostic_results.get("symptoms_identified", [])
            severity = diagnostic_results.get("severity_assessment", {})
            patient_context = inputs.get("patient_context", {})
            
            # Generate treatment recommendations
            symptomatic_treatments = self._get_symptomatic_treatments(symptoms)
            neuroprotective_treatments = self._get_neuroprotective_treatments(severity)
            rehabilitation_recommendations = self._get_rehabilitation_recommendations(symptoms)
            
            # Check contraindications
            contraindication_warnings = self._check_contraindications(
                symptomatic_treatments + neuroprotective_treatments, patient_context
            )
            
            # Generate comprehensive treatment plan
            treatment_plan = await self._generate_treatment_plan(
                symptoms, severity, symptomatic_treatments, 
                neuroprotective_treatments, rehabilitation_recommendations
            )
            
            return {
                "symptomatic_treatments": symptomatic_treatments,
                "neuroprotective_treatments": neuroprotective_treatments,
                "rehabilitation_recommendations": rehabilitation_recommendations,
                "contraindication_warnings": contraindication_warnings,
                "comprehensive_plan": treatment_plan,
                "evidence_quality": self._assess_evidence_quality(
                    symptomatic_treatments + neuroprotective_treatments
                )
            }
            
        except Exception as e:
            self.logger.error(f"Error suggesting treatments: {e}")
            return {"error": str(e)}
    
    async def _check_drug_interactions(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Check for drug interactions"""
        try:
            medications = inputs.get("medications", [])
            proposed_treatments = inputs.get("proposed_treatments", [])
            
            interactions = []
            warnings = []
            
            # Check interactions between existing medications and proposed treatments
            for med in medications:
                for treatment in proposed_treatments:
                    interaction = self._check_interaction(med, treatment)
                    if interaction:
                        interactions.append(interaction)
            
            # Check interactions within proposed treatments
            for i, treatment1 in enumerate(proposed_treatments):
                for treatment2 in proposed_treatments[i+1:]:
                    interaction = self._check_interaction(treatment1, treatment2)
                    if interaction:
                        interactions.append(interaction)
            
            # Generate monitoring recommendations
            monitoring_recommendations = self._get_monitoring_recommendations(
                medications + proposed_treatments
            )
            
            return {
                "interactions_found": len(interactions) > 0,
                "interactions": interactions,
                "warnings": warnings,
                "monitoring_recommendations": monitoring_recommendations,
                "safety_assessment": "safe" if len(interactions) == 0 else "caution_required"
            }
            
        except Exception as e:
            self.logger.error(f"Error checking drug interactions: {e}")
            return {"error": str(e)}
    
    async def _protocol_recommendations(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Provide protocol-based treatment recommendations"""
        try:
            condition = inputs.get("condition", "neurocovid")
            severity = inputs.get("severity", "moderate")
            patient_factors = inputs.get("patient_factors", {})
            
            # Generate protocol recommendations using AI
            prompt = f"""
            As a neurologist specializing in COVID-19 neurological manifestations, provide evidence-based treatment protocols for:
            
            Condition: {condition}
            Severity: {severity}
            Patient Factors: {json.dumps(patient_factors, indent=2)}
            
            Please provide:
            1. First-line treatment recommendations
            2. Second-line alternatives
            3. Monitoring requirements
            4. Duration of treatment
            5. Success criteria
            6. When to escalate care
            
            Base recommendations on current medical guidelines and evidence.
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.1, "num_predict": 1024}
            )
            
            protocol_text = response["message"]["content"]
            
            # Structure the protocol
            structured_protocol = self._parse_treatment_protocol(protocol_text)
            
            return {
                "protocol": structured_protocol,
                "raw_recommendations": protocol_text,
                "evidence_level": "moderate",
                "last_updated": "2024"
            }
            
        except Exception as e:
            self.logger.error(f"Error generating protocol recommendations: {e}")
            return {"error": str(e)}
    
    async def _rehabilitation_plan(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive rehabilitation plan"""
        try:
            symptoms = inputs.get("symptoms", [])
            functional_status = inputs.get("functional_status", {})
            goals = inputs.get("goals", [])
            
            # Determine rehabilitation needs
            rehab_needs = self._assess_rehabilitation_needs(symptoms)
            
            # Create multidisciplinary plan
            multidisciplinary_plan = {
                "physical_therapy": self._get_pt_recommendations(symptoms),
                "occupational_therapy": self._get_ot_recommendations(symptoms),
                "speech_therapy": self._get_st_recommendations(symptoms),
                "cognitive_rehabilitation": self._get_cognitive_rehab(symptoms),
                "psychological_support": self._get_psych_support(symptoms)
            }
            
            # Generate timeline and goals
            timeline = self._create_rehabilitation_timeline(rehab_needs)
            
            return {
                "rehabilitation_needs": rehab_needs,
                "multidisciplinary_plan": multidisciplinary_plan,
                "timeline": timeline,
                "outcome_measures": self._get_outcome_measures(symptoms),
                "discharge_criteria": self._get_discharge_criteria(symptoms)
            }
            
        except Exception as e:
            self.logger.error(f"Error creating rehabilitation plan: {e}")
            return {"error": str(e)}
    
    def _get_symptomatic_treatments(self, symptoms: List[str]) -> List[Dict[str, Any]]:
        """Get symptomatic treatment recommendations"""
        treatments = []
        
        for symptom in symptoms:
            if symptom in self.treatment_knowledge["neurocovid_treatments"]["symptomatic"]:
                symptom_treatments = self.treatment_knowledge["neurocovid_treatments"]["symptomatic"][symptom]
                for treatment in symptom_treatments:
                    treatments.append({
                        "medication": treatment,
                        "indication": symptom,
                        "type": "symptomatic",
                        "evidence_level": self._get_evidence_level(treatment)
                    })
        
        return treatments
    
    def _get_neuroprotective_treatments(self, severity: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get neuroprotective treatment recommendations"""
        treatments = []
        
        if severity.get("overall_severity") in ["moderate", "severe"]:
            for treatment in self.treatment_knowledge["neurocovid_treatments"]["neuroprotective"]:
                treatments.append({
                    "medication": treatment,
                    "indication": "neuroprotection",
                    "type": "neuroprotective",
                    "evidence_level": self._get_evidence_level(treatment)
                })
        
        return treatments
    
    def _get_rehabilitation_recommendations(self, symptoms: List[str]) -> List[Dict[str, Any]]:
        """Get rehabilitation recommendations"""
        recommendations = []
        
        rehab_types = self.treatment_knowledge["neurocovid_treatments"]["rehabilitation"]
        
        # Determine which rehabilitation types are needed
        if any(s in ["cognitive_impairment", "memory_issues", "confusion"] for s in symptoms):
            recommendations.append({
                "type": "cognitive_rehabilitation",
                "priority": "high",
                "duration": "3-6 months"
            })
        
        if any(s in ["fatigue", "weakness", "mobility_issues"] for s in symptoms):
            recommendations.append({
                "type": "physical_therapy",
                "priority": "high",
                "duration": "2-4 months"
            })
        
        if any(s in ["speech_issues", "swallowing_problems"] for s in symptoms):
            recommendations.append({
                "type": "speech_therapy",
                "priority": "high",
                "duration": "1-3 months"
            })
        
        return recommendations
    
    def _check_contraindications(self, treatments: List[Dict], patient_context: Dict) -> List[Dict]:
        """Check for contraindications"""
        warnings = []
        
        patient_conditions = patient_context.get("conditions", [])
        
        for treatment in treatments:
            medication = treatment.get("medication", "")
            if medication in self.treatment_knowledge["contraindications"]:
                contraindications = self.treatment_knowledge["contraindications"][medication]
                for condition in patient_conditions:
                    if condition in contraindications:
                        warnings.append({
                            "medication": medication,
                            "contraindication": condition,
                            "severity": "high",
                            "recommendation": "avoid_or_use_alternative"
                        })
        
        return warnings
    
    def _check_interaction(self, med1: str, med2: str) -> Optional[Dict]:
        """Check for drug interaction between two medications"""
        # Simplified interaction checking
        if med1 in self.treatment_knowledge["drug_interactions"]:
            if med2 in self.treatment_knowledge["drug_interactions"][med1]:
                return {
                    "medication1": med1,
                    "medication2": med2,
                    "interaction_type": "moderate",
                    "clinical_significance": "monitor_closely"
                }
        return None
    
    def _get_monitoring_recommendations(self, medications: List[str]) -> List[Dict]:
        """Get monitoring recommendations for medications"""
        monitoring = []
        
        for med in medications:
            if med in self.treatment_knowledge["monitoring_requirements"]:
                requirements = self.treatment_knowledge["monitoring_requirements"][med]
                for requirement in requirements:
                    monitoring.append({
                        "medication": med,
                        "parameter": requirement,
                        "frequency": "weekly" if "blood" in requirement else "monthly"
                    })
        
        return monitoring
    
    def _get_evidence_level(self, treatment: str) -> str:
        """Get evidence level for treatment"""
        for level, treatments in self.treatment_knowledge["evidence_levels"].items():
            if any(treatment in t for t in treatments):
                return level
        return "low"
    
    def _assess_evidence_quality(self, treatments: List[Dict]) -> Dict[str, Any]:
        """Assess overall evidence quality"""
        evidence_counts = {"high": 0, "moderate": 0, "low": 0}
        
        for treatment in treatments:
            level = treatment.get("evidence_level", "low")
            evidence_counts[level] += 1
        
        total = sum(evidence_counts.values())
        if total == 0:
            return {"overall": "insufficient", "breakdown": evidence_counts}
        
        high_percentage = evidence_counts["high"] / total
        if high_percentage >= 0.6:
            overall = "high"
        elif high_percentage >= 0.3:
            overall = "moderate"
        else:
            overall = "low"
        
        return {"overall": overall, "breakdown": evidence_counts}
    
    async def _generate_treatment_plan(self, symptoms: List[str], severity: Dict,
                                     symptomatic: List[Dict], neuroprotective: List[Dict],
                                     rehabilitation: List[Dict]) -> str:
        """Generate comprehensive treatment plan"""
        try:
            prompt = f"""
            Create a comprehensive treatment plan for a patient with neurocovid:
            
            Symptoms: {', '.join(symptoms)}
            Severity: {json.dumps(severity, indent=2)}
            Symptomatic Treatments: {json.dumps(symptomatic, indent=2)}
            Neuroprotective Treatments: {json.dumps(neuroprotective, indent=2)}
            Rehabilitation: {json.dumps(rehabilitation, indent=2)}
            
            Provide a structured treatment plan with:
            1. Immediate interventions
            2. Short-term management (1-4 weeks)
            3. Long-term management (1-6 months)
            4. Monitoring schedule
            5. Follow-up recommendations
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.1, "num_predict": 1024}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating treatment plan: {e}")
            return "Error generating treatment plan"
    
    def _parse_treatment_protocol(self, text: str) -> Dict[str, Any]:
        """Parse treatment protocol text into structured format"""
        # Simplified parsing - in production, this could be more sophisticated
        return {
            "first_line": ["Symptomatic management", "Supportive care"],
            "second_line": ["Corticosteroids if indicated", "Rehabilitation"],
            "monitoring": ["Neurological status", "Functional improvement"],
            "raw_text": text
        }
    
    def _assess_rehabilitation_needs(self, symptoms: List[str]) -> Dict[str, str]:
        """Assess rehabilitation needs based on symptoms"""
        needs = {}
        
        if any(s in ["cognitive_impairment", "memory_issues"] for s in symptoms):
            needs["cognitive"] = "high"
        if any(s in ["fatigue", "weakness"] for s in symptoms):
            needs["physical"] = "moderate"
        if any(s in ["speech_issues"] for s in symptoms):
            needs["speech"] = "high"
        if any(s in ["depression", "anxiety"] for s in symptoms):
            needs["psychological"] = "moderate"
        
        return needs
    
    def _get_pt_recommendations(self, symptoms: List[str]) -> List[str]:
        """Get physical therapy recommendations"""
        recommendations = ["General conditioning", "Endurance training"]
        if "fatigue" in symptoms:
            recommendations.append("Graded exercise therapy")
        if "weakness" in symptoms:
            recommendations.append("Strength training")
        return recommendations
    
    def _get_ot_recommendations(self, symptoms: List[str]) -> List[str]:
        """Get occupational therapy recommendations"""
        recommendations = ["Activities of daily living assessment"]
        if "cognitive_impairment" in symptoms:
            recommendations.append("Cognitive strategies training")
        if "fatigue" in symptoms:
            recommendations.append("Energy conservation techniques")
        return recommendations
    
    def _get_st_recommendations(self, symptoms: List[str]) -> List[str]:
        """Get speech therapy recommendations"""
        recommendations = []
        if "speech_issues" in symptoms:
            recommendations.append("Speech articulation therapy")
        if "swallowing_problems" in symptoms:
            recommendations.append("Swallowing assessment and therapy")
        return recommendations
    
    def _get_cognitive_rehab(self, symptoms: List[str]) -> List[str]:
        """Get cognitive rehabilitation recommendations"""
        recommendations = []
        if "cognitive_impairment" in symptoms:
            recommendations.extend([
                "Memory training exercises",
                "Attention training",
                "Executive function training"
            ])
        return recommendations
    
    def _get_psych_support(self, symptoms: List[str]) -> List[str]:
        """Get psychological support recommendations"""
        recommendations = []
        if any(s in ["depression", "anxiety", "mood_changes"] for s in symptoms):
            recommendations.extend([
                "Psychological counseling",
                "Cognitive behavioral therapy",
                "Support groups"
            ])
        return recommendations
    
    def _create_rehabilitation_timeline(self, needs: Dict[str, str]) -> Dict[str, str]:
        """Create rehabilitation timeline"""
        timeline = {}
        
        for need_type, priority in needs.items():
            if priority == "high":
                timeline[need_type] = "Start immediately, 3-6 months duration"
            elif priority == "moderate":
                timeline[need_type] = "Start within 2 weeks, 2-4 months duration"
            else:
                timeline[need_type] = "Start within 1 month, 1-2 months duration"
        
        return timeline
    
    def _get_outcome_measures(self, symptoms: List[str]) -> List[str]:
        """Get outcome measures for tracking progress"""
        measures = ["Functional Independence Measure (FIM)"]
        
        if "cognitive_impairment" in symptoms:
            measures.append("Montreal Cognitive Assessment (MoCA)")
        if "fatigue" in symptoms:
            measures.append("Fatigue Severity Scale")
        if "depression" in symptoms:
            measures.append("Patient Health Questionnaire (PHQ-9)")
        
        return measures
    
    def _get_discharge_criteria(self, symptoms: List[str]) -> List[str]:
        """Get discharge criteria"""
        return [
            "Functional independence in activities of daily living",
            "Stable neurological status",
            "Adequate family/caregiver support",
            "Appropriate follow-up arranged"
        ]
    
    async def _handle_request(self, message: Message):
        """Handle incoming request"""
        try:
            result = await self.process_request(message.content)
            
            response = Message(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.RESPONSE,
                content=result,
                correlation_id=message.correlation_id
            )
            
            await self.send_message(response)
            
        except Exception as e:
            self.logger.error(f"Error handling request: {e}")
