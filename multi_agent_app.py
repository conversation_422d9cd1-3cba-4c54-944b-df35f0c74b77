"""
Multi-Agent Medical System - Enhanced Neurocovid Analysis
"""
import streamlit as st
import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, Any, List
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.agent_manager import agent_manager
from security.medical_security import compliance_manager
from config.settings import STREAMLIT_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure Streamlit page
st.set_page_config(
    page_title=STREAMLIT_CONFIG["page_title"],
    page_icon=STREAMLIT_CONFIG["page_icon"],
    layout=STREAMLIT_CONFIG["layout"],
    initial_sidebar_state=STREAMLIT_CONFIG["initial_sidebar_state"]
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 4px solid #1f77b4;
    }
    .workflow-result {
        background-color: #e8f4f8;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        border-left: 4px solid #17a2b8;
    }
    .security-badge {
        background-color: #d4edda;
        color: #155724;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.8rem;
        font-weight: bold;
    }
    .agent-status {
        display: inline-block;
        padding: 0.2rem 0.5rem;
        border-radius: 0.3rem;
        font-size: 0.8rem;
        font-weight: bold;
    }
    .status-healthy { background-color: #d4edda; color: #155724; }
    .status-warning { background-color: #fff3cd; color: #856404; }
    .status-error { background-color: #f8d7da; color: #721c24; }
</style>
""", unsafe_allow_html=True)


@st.cache_resource
def initialize_system():
    """Initialize the multi-agent system"""
    return agent_manager


async def run_async_function(coro):
    """Run async function in Streamlit"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return await coro


def display_agent_status():
    """Display agent status in sidebar"""
    with st.sidebar:
        st.header("🤖 Agent Status")
        
        try:
            status = agent_manager.get_agent_status()
            
            # System status
            system_status = "🟢 Online" if status["system_running"] else "🔴 Offline"
            st.markdown(f"**System:** {system_status}")
            st.markdown(f"**Uptime:** {status['system_uptime']:.1f}s")
            st.markdown(f"**Requests:** {status['total_requests_processed']}")
            
            # Agent statuses
            st.subheader("Agents")
            for agent_id, agent_info in status["agents"].items():
                agent_status = agent_info["status"]
                status_class = {
                    "idle": "status-healthy",
                    "busy": "status-warning", 
                    "error": "status-error",
                    "offline": "status-error"
                }.get(agent_status, "status-warning")
                
                st.markdown(f"""
                <div class="agent-card">
                    <strong>{agent_info['name']}</strong><br>
                    <span class="agent-status {status_class}">{agent_status.upper()}</span><br>
                    <small>Messages: {agent_info['messages_processed']} | Errors: {agent_info['errors_count']}</small>
                </div>
                """, unsafe_allow_html=True)
                
        except Exception as e:
            st.error(f"Error getting agent status: {e}")


def display_security_status():
    """Display security and compliance status"""
    with st.sidebar:
        st.header("🔒 Security & Compliance")
        
        try:
            compliance_report = compliance_manager.generate_compliance_report()
            
            st.markdown('<span class="security-badge">HIPAA Compliant</span>', unsafe_allow_html=True)
            st.markdown('<span class="security-badge">Encrypted</span>', unsafe_allow_html=True)
            st.markdown('<span class="security-badge">Audited</span>', unsafe_allow_html=True)
            
            st.markdown(f"**Success Rate:** {compliance_report['metrics']['success_rate']:.1%}")
            st.markdown(f"**Active Sessions:** {compliance_report['active_sessions']}")
            
        except Exception as e:
            st.warning(f"Security status unavailable: {e}")


def main():
    """Main application function"""
    
    # Header
    st.markdown('<h1 class="main-header">🧠 Multi-Agent Medical System</h1>', unsafe_allow_html=True)
    st.markdown("**Advanced Neurocovid Analysis with Specialized AI Agents**")
    st.markdown("---")
    
    # Initialize system
    system = initialize_system()
    
    # Check if system is initialized
    if not system.running:
        with st.spinner("Initializing multi-agent system..."):
            try:
                asyncio.run(system.initialize())
                st.success("✅ Multi-agent system initialized successfully!")
                st.rerun()
            except Exception as e:
                st.error(f"❌ Failed to initialize system: {e}")
                st.stop()
    
    # Display status in sidebar
    display_agent_status()
    display_security_status()
    
    # Main interface tabs
    tab1, tab2, tab3, tab4 = st.tabs(["🔍 Query Analysis", "📄 Document Management", "📊 System Monitoring", "⚙️ Settings"])
    
    with tab1:
        st.header("Medical Query Analysis")
        
        # Workflow selection
        col1, col2 = st.columns([2, 1])
        
        with col1:
            query = st.text_area(
                "Enter your medical query:",
                placeholder="e.g., What are the neurological manifestations of COVID-19?",
                height=100
            )
        
        with col2:
            workflow_templates = system.get_workflow_templates()
            workflow_type = st.selectbox(
                "Analysis Type:",
                options=list(workflow_templates.get("available_workflows", ["comprehensive_analysis"])),
                help="Select the type of analysis to perform"
            )
            
            # Patient context (optional)
            with st.expander("Patient Context (Optional)"):
                age = st.number_input("Age", min_value=0, max_value=120, value=0)
                conditions = st.multiselect(
                    "Existing Conditions",
                    ["diabetes", "hypertension", "heart_disease", "kidney_disease", "immunocompromised"]
                )
                medications = st.text_input("Current Medications (comma-separated)")
        
        # Process query
        if st.button("🚀 Analyze Query", type="primary"):
            if query.strip():
                # Prepare patient context
                patient_context = {
                    "age": age if age > 0 else None,
                    "conditions": conditions,
                    "medications": [med.strip() for med in medications.split(",") if med.strip()]
                }
                
                with st.spinner("Processing query through multi-agent system..."):
                    try:
                        # Process with compliance
                        session_token = "demo_session"  # In production, use real authentication
                        
                        # Authenticate demo user
                        auth_result = compliance_manager.access_control.authenticate_user(
                            "demo_user", "physician", {}
                        )
                        session_token = auth_result["session_token"]
                        
                        # Process query
                        result = asyncio.run(
                            system.process_query(query, workflow_type, patient_context)
                        )
                        
                        if "error" in result:
                            st.error(f"❌ Error: {result['error']}")
                        else:
                            # Display results
                            st.markdown("### 📋 Analysis Results")
                            
                            # Workflow summary
                            st.markdown(f"""
                            <div class="workflow-result">
                                <h4>Workflow: {workflow_type}</h4>
                                <p><strong>Status:</strong> {result.get('status', 'Unknown')}</p>
                                <p><strong>Workflow ID:</strong> {result.get('workflow_id', 'N/A')}</p>
                            </div>
                            """, unsafe_allow_html=True)
                            
                            # Final answer
                            if "result" in result and "final_answer" in result["result"]:
                                st.markdown("### 🎯 Final Analysis")
                                st.markdown(result["result"]["final_answer"])
                            
                            # Step results
                            if "result" in result and "steps_results" in result["result"]:
                                st.markdown("### 🔍 Detailed Results by Agent")
                                
                                steps = result["result"]["steps_results"]
                                
                                for step_name, step_result in steps.items():
                                    with st.expander(f"📊 {step_name.replace('_', ' ').title()}"):
                                        if isinstance(step_result, dict):
                                            st.json(step_result)
                                        else:
                                            st.write(step_result)
                            
                            # Execution metrics
                            if "result" in result:
                                execution_time = result["result"].get("execution_time", 0)
                                st.markdown(f"**⏱️ Execution Time:** {execution_time:.2f} seconds")
                        
                    except Exception as e:
                        st.error(f"❌ Error processing query: {e}")
                        logger.error(f"Query processing error: {e}")
            else:
                st.warning("⚠️ Please enter a query to analyze")
    
    with tab2:
        st.header("Document Management")
        
        # Document upload
        st.subheader("📤 Upload Medical Documents")
        uploaded_files = st.file_uploader(
            "Upload PDF documents:",
            type=["pdf"],
            accept_multiple_files=True,
            help="Upload medical literature, case studies, or research papers"
        )
        
        if uploaded_files and st.button("📁 Process Documents"):
            with st.spinner("Processing documents..."):
                try:
                    # Prepare documents for processing
                    documents = []
                    for file in uploaded_files:
                        # In a real implementation, you'd extract text from PDFs
                        documents.append({
                            "filename": file.name,
                            "text": f"Sample text from {file.name}",  # Placeholder
                            "page": 1,
                            "chunk_id": f"chunk_{file.name}"
                        })
                    
                    # Add documents to system
                    result = asyncio.run(system.add_documents(documents))
                    
                    if "error" in result:
                        st.error(f"❌ Error: {result['error']}")
                    else:
                        st.success(f"✅ Successfully processed {result.get('documents_added', 0)} documents")
                
                except Exception as e:
                    st.error(f"❌ Error processing documents: {e}")
        
        # Document statistics
        st.subheader("📊 Document Statistics")
        try:
            doc_agent = system.agents.get("document_intelligence")
            if doc_agent and hasattr(doc_agent, 'get_collection_stats'):
                stats = doc_agent.get_collection_stats()
                
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("Total Documents", stats.get("total_documents", 0))
                with col2:
                    st.metric("Collection", stats.get("collection_name", "N/A"))
        except Exception as e:
            st.warning(f"Unable to load document statistics: {e}")
    
    with tab3:
        st.header("System Monitoring")
        
        # System metrics
        try:
            metrics = system.get_system_metrics()
            
            # System overview
            st.subheader("📈 System Metrics")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Uptime", f"{metrics['system_metrics']['uptime_seconds']:.1f}s")
            with col2:
                st.metric("Total Requests", metrics['system_metrics']['total_requests'])
            with col3:
                st.metric("Error Rate", f"{metrics['system_metrics']['error_rate']:.1%}")
            with col4:
                st.metric("Active Agents", metrics['system_metrics']['agents_count'])
            
            # Agent performance
            st.subheader("🤖 Agent Performance")
            for agent_id, agent_metrics in metrics["agent_metrics"].items():
                with st.expander(f"Agent: {agent_id}"):
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Messages Processed", agent_metrics["messages_processed"])
                    with col2:
                        st.metric("Errors", agent_metrics["errors_count"])
                    with col3:
                        st.metric("Status", agent_metrics["status"])
            
            # Health check
            if st.button("🔍 Run Health Check"):
                with st.spinner("Running health check..."):
                    health = asyncio.run(system.health_check())
                    
                    if health["system_healthy"]:
                        st.success("✅ System is healthy")
                    else:
                        st.error("❌ System health issues detected")
                    
                    st.json(health)
        
        except Exception as e:
            st.error(f"Error loading system metrics: {e}")
    
    with tab4:
        st.header("System Settings")
        
        # Agent capabilities
        st.subheader("🎯 Agent Capabilities")
        try:
            capabilities = system.get_agent_capabilities()
            
            for agent_id, agent_info in capabilities.items():
                with st.expander(f"{agent_info['name']}"):
                    st.markdown(f"**Role:** {agent_info['role']}")
                    st.markdown("**Capabilities:**")
                    for capability in agent_info['capabilities']:
                        st.markdown(f"• {capability.replace('_', ' ').title()}")
        
        except Exception as e:
            st.error(f"Error loading agent capabilities: {e}")
        
        # System controls
        st.subheader("🔧 System Controls")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🔄 Restart System"):
                with st.spinner("Restarting system..."):
                    try:
                        asyncio.run(system.shutdown())
                        asyncio.run(system.initialize())
                        st.success("✅ System restarted successfully")
                        st.rerun()
                    except Exception as e:
                        st.error(f"❌ Error restarting system: {e}")
        
        with col2:
            if st.button("📊 Generate Compliance Report"):
                try:
                    report = compliance_manager.generate_compliance_report()
                    st.json(report)
                except Exception as e:
                    st.error(f"Error generating compliance report: {e}")


if __name__ == "__main__":
    main()
