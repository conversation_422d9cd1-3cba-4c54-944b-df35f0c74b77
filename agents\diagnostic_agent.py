"""
Diagnostic Analysis Agent - Specialized in medical diagnosis and symptom analysis
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
import ollama
import json

from .base_agent import BaseAgent, Message, MessageType
from config.settings import OLLAMA_CONFIG

logger = logging.getLogger(__name__)


class DiagnosticAnalysisAgent(BaseAgent):
    """Agent specialized in medical diagnosis and symptom analysis"""
    
    def __init__(self):
        super().__init__(
            agent_id="diagnostic_analysis",
            name="Diagnostic Analysis Agent",
            role="Medical diagnosis and symptom analysis specialist",
            capabilities=[
                "symptom_analysis",
                "differential_diagnosis",
                "risk_assessment",
                "clinical_correlation",
                "neurocovid_expertise"
            ]
        )
        
        self.ollama_client = None
        self.medical_knowledge = self._initialize_medical_knowledge()
        
        # Initialize Ollama client
        asyncio.create_task(self._initialize_ollama())
        
        # Register message handlers
        self.message_handlers[MessageType.REQUEST] = self._handle_request
    
    async def _initialize_ollama(self):
        """Initialize Ollama client"""
        try:
            self.ollama_client = ollama.Client(host=OLLAMA_CONFIG["base_url"])
            self.logger.info("Diagnostic Analysis Agent initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing Ollama client: {e}")
    
    def _initialize_medical_knowledge(self) -> Dict[str, Any]:
        """Initialize medical knowledge base for neurocovid"""
        return {
            "neurocovid_symptoms": {
                "neurological": [
                    "anosmia", "ageusia", "headache", "dizziness", "confusion",
                    "delirium", "seizures", "stroke", "encephalitis", "meningitis",
                    "neuropathy", "myopathy", "cognitive_impairment"
                ],
                "psychiatric": [
                    "anxiety", "depression", "psychosis", "mood_changes",
                    "sleep_disorders", "fatigue", "brain_fog"
                ],
                "autonomic": [
                    "dysautonomia", "heart_rate_variability", "blood_pressure_changes",
                    "temperature_regulation", "gastrointestinal_dysfunction"
                ]
            },
            "severity_indicators": {
                "mild": ["anosmia", "ageusia", "mild_headache", "fatigue"],
                "moderate": ["persistent_headache", "confusion", "memory_issues", "mood_changes"],
                "severe": ["seizures", "stroke", "encephalitis", "severe_delirium", "coma"]
            },
            "risk_factors": [
                "advanced_age", "diabetes", "hypertension", "cardiovascular_disease",
                "immunocompromised", "previous_neurological_conditions"
            ],
            "diagnostic_criteria": {
                "neurocovid": [
                    "confirmed_covid19_infection",
                    "neurological_symptoms_onset",
                    "temporal_relationship",
                    "exclusion_other_causes"
                ]
            }
        }
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming diagnostic request"""
        action = request.get("action", "")
        inputs = request.get("inputs", {})
        
        if action == "analyze_symptoms":
            return await self._analyze_symptoms(inputs)
        elif action == "differential_diagnosis":
            return await self._differential_diagnosis(inputs)
        elif action == "risk_assessment":
            return await self._risk_assessment(inputs)
        elif action == "detailed_analysis":
            return await self._detailed_analysis(inputs)
        else:
            return {"error": f"Unknown action: {action}"}
    
    async def _analyze_symptoms(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze symptoms and provide diagnostic insights"""
        try:
            query = inputs.get("query", "")
            document_results = inputs.get("document_intelligence_result", {})
            
            # Extract symptoms from query and documents
            symptoms = await self._extract_symptoms(query, document_results)
            
            # Categorize symptoms
            categorized_symptoms = self._categorize_symptoms(symptoms)
            
            # Assess severity
            severity_assessment = self._assess_severity(symptoms)
            
            # Generate diagnostic insights
            diagnostic_insights = await self._generate_diagnostic_insights(
                query, symptoms, categorized_symptoms, severity_assessment
            )
            
            return {
                "symptoms_identified": symptoms,
                "symptom_categories": categorized_symptoms,
                "severity_assessment": severity_assessment,
                "diagnostic_insights": diagnostic_insights,
                "neurocovid_likelihood": self._calculate_neurocovid_likelihood(symptoms)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing symptoms: {e}")
            return {"error": str(e)}
    
    async def _differential_diagnosis(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Generate differential diagnosis"""
        try:
            symptoms = inputs.get("symptoms", [])
            patient_context = inputs.get("patient_context", {})
            
            # Generate differential diagnosis using AI
            prompt = f"""
            As a neurologist specializing in COVID-19 neurological manifestations, provide a differential diagnosis for the following presentation:
            
            Symptoms: {', '.join(symptoms)}
            Patient Context: {json.dumps(patient_context, indent=2)}
            
            Please provide:
            1. Most likely diagnoses (ranked by probability)
            2. Key distinguishing features
            3. Recommended diagnostic tests
            4. Red flags to watch for
            
            Focus on neurocovid and related conditions.
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.2, "num_predict": 1024}
            )
            
            differential_text = response["message"]["content"]
            
            # Parse and structure the response
            structured_differential = self._parse_differential_diagnosis(differential_text)
            
            return {
                "differential_diagnosis": structured_differential,
                "raw_analysis": differential_text,
                "confidence_level": "high" if len(symptoms) >= 3 else "moderate"
            }
            
        except Exception as e:
            self.logger.error(f"Error generating differential diagnosis: {e}")
            return {"error": str(e)}
    
    async def _risk_assessment(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Assess patient risk factors and prognosis"""
        try:
            symptoms = inputs.get("symptoms", [])
            risk_factors = inputs.get("risk_factors", [])
            
            # Calculate risk scores
            neurological_risk = self._calculate_neurological_risk(symptoms, risk_factors)
            progression_risk = self._calculate_progression_risk(symptoms)
            
            # Generate risk assessment
            risk_assessment = {
                "overall_risk": "low",
                "neurological_risk_score": neurological_risk,
                "progression_risk_score": progression_risk,
                "risk_factors_present": risk_factors,
                "monitoring_recommendations": [],
                "intervention_urgency": "routine"
            }
            
            # Determine overall risk level
            if neurological_risk >= 7 or progression_risk >= 8:
                risk_assessment["overall_risk"] = "high"
                risk_assessment["intervention_urgency"] = "urgent"
            elif neurological_risk >= 4 or progression_risk >= 5:
                risk_assessment["overall_risk"] = "moderate"
                risk_assessment["intervention_urgency"] = "prompt"
            
            # Add monitoring recommendations
            risk_assessment["monitoring_recommendations"] = self._generate_monitoring_recommendations(
                risk_assessment["overall_risk"], symptoms
            )
            
            return risk_assessment
            
        except Exception as e:
            self.logger.error(f"Error in risk assessment: {e}")
            return {"error": str(e)}
    
    async def _detailed_analysis(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform detailed diagnostic analysis"""
        try:
            query = inputs.get("query", "")
            document_results = inputs.get("document_intelligence_result", {})
            
            # Perform comprehensive analysis
            symptom_analysis = await self._analyze_symptoms(inputs)
            differential = await self._differential_diagnosis({
                "symptoms": symptom_analysis.get("symptoms_identified", []),
                "patient_context": {"query": query}
            })
            risk_assessment = await self._risk_assessment({
                "symptoms": symptom_analysis.get("symptoms_identified", []),
                "risk_factors": []
            })
            
            # Generate comprehensive report
            comprehensive_report = await self._generate_comprehensive_report(
                query, symptom_analysis, differential, risk_assessment, document_results
            )
            
            return {
                "symptom_analysis": symptom_analysis,
                "differential_diagnosis": differential,
                "risk_assessment": risk_assessment,
                "comprehensive_report": comprehensive_report,
                "analysis_confidence": self._calculate_analysis_confidence(
                    symptom_analysis, differential, risk_assessment
                )
            }
            
        except Exception as e:
            self.logger.error(f"Error in detailed analysis: {e}")
            return {"error": str(e)}
    
    async def _extract_symptoms(self, query: str, document_results: Dict) -> List[str]:
        """Extract symptoms from query and document results"""
        symptoms = []
        
        # Extract from query
        query_lower = query.lower()
        for category, symptom_list in self.medical_knowledge["neurocovid_symptoms"].items():
            for symptom in symptom_list:
                if symptom.replace("_", " ") in query_lower:
                    symptoms.append(symptom)
        
        # Extract from document results if available
        if document_results and "documents" in document_results:
            for doc in document_results["documents"][:3]:  # Check top 3 documents
                content_lower = doc.get("content", "").lower()
                for category, symptom_list in self.medical_knowledge["neurocovid_symptoms"].items():
                    for symptom in symptom_list:
                        if symptom.replace("_", " ") in content_lower:
                            if symptom not in symptoms:
                                symptoms.append(symptom)
        
        return symptoms
    
    def _categorize_symptoms(self, symptoms: List[str]) -> Dict[str, List[str]]:
        """Categorize symptoms by type"""
        categorized = {"neurological": [], "psychiatric": [], "autonomic": [], "other": []}
        
        for symptom in symptoms:
            categorized_flag = False
            for category, symptom_list in self.medical_knowledge["neurocovid_symptoms"].items():
                if symptom in symptom_list:
                    categorized[category].append(symptom)
                    categorized_flag = True
                    break
            
            if not categorized_flag:
                categorized["other"].append(symptom)
        
        return categorized
    
    def _assess_severity(self, symptoms: List[str]) -> Dict[str, Any]:
        """Assess symptom severity"""
        severity_scores = {"mild": 0, "moderate": 0, "severe": 0}
        
        for symptom in symptoms:
            for severity, symptom_list in self.medical_knowledge["severity_indicators"].items():
                if symptom in symptom_list:
                    severity_scores[severity] += 1
        
        # Determine overall severity
        if severity_scores["severe"] > 0:
            overall_severity = "severe"
        elif severity_scores["moderate"] > 0:
            overall_severity = "moderate"
        else:
            overall_severity = "mild"
        
        return {
            "overall_severity": overall_severity,
            "severity_breakdown": severity_scores,
            "total_symptoms": len(symptoms)
        }
    
    def _calculate_neurocovid_likelihood(self, symptoms: List[str]) -> Dict[str, Any]:
        """Calculate likelihood of neurocovid diagnosis"""
        neurocovid_symptoms = []
        for category_symptoms in self.medical_knowledge["neurocovid_symptoms"].values():
            neurocovid_symptoms.extend(category_symptoms)
        
        matching_symptoms = [s for s in symptoms if s in neurocovid_symptoms]
        likelihood_score = len(matching_symptoms) / max(len(neurocovid_symptoms), 1)
        
        if likelihood_score >= 0.3:
            likelihood = "high"
        elif likelihood_score >= 0.15:
            likelihood = "moderate"
        else:
            likelihood = "low"
        
        return {
            "likelihood": likelihood,
            "score": likelihood_score,
            "matching_symptoms": matching_symptoms,
            "total_possible_symptoms": len(neurocovid_symptoms)
        }
    
    def _calculate_neurological_risk(self, symptoms: List[str], risk_factors: List[str]) -> int:
        """Calculate neurological risk score (0-10)"""
        score = 0
        
        # Symptom-based scoring
        severe_symptoms = ["seizures", "stroke", "encephalitis", "severe_delirium"]
        moderate_symptoms = ["confusion", "persistent_headache", "memory_issues"]
        
        for symptom in symptoms:
            if symptom in severe_symptoms:
                score += 3
            elif symptom in moderate_symptoms:
                score += 2
            else:
                score += 1
        
        # Risk factor scoring
        high_risk_factors = ["advanced_age", "immunocompromised", "previous_neurological_conditions"]
        for factor in risk_factors:
            if factor in high_risk_factors:
                score += 2
            else:
                score += 1
        
        return min(score, 10)
    
    def _calculate_progression_risk(self, symptoms: List[str]) -> int:
        """Calculate risk of symptom progression (0-10)"""
        progression_indicators = ["confusion", "delirium", "cognitive_impairment", "seizures"]
        score = sum(2 for symptom in symptoms if symptom in progression_indicators)
        return min(score, 10)
    
    def _generate_monitoring_recommendations(self, risk_level: str, symptoms: List[str]) -> List[str]:
        """Generate monitoring recommendations based on risk level"""
        recommendations = []
        
        if risk_level == "high":
            recommendations.extend([
                "Continuous neurological monitoring",
                "Daily cognitive assessments",
                "Frequent vital sign monitoring",
                "Consider ICU admission if not already"
            ])
        elif risk_level == "moderate":
            recommendations.extend([
                "Regular neurological checks (every 4-6 hours)",
                "Daily cognitive screening",
                "Monitor for symptom progression"
            ])
        else:
            recommendations.extend([
                "Daily neurological assessment",
                "Patient/family education on warning signs",
                "Follow-up in 24-48 hours"
            ])
        
        # Symptom-specific recommendations
        if "seizures" in symptoms:
            recommendations.append("EEG monitoring")
        if "headache" in symptoms:
            recommendations.append("Monitor for signs of increased intracranial pressure")
        if "confusion" in symptoms:
            recommendations.append("Delirium screening tools")
        
        return recommendations
    
    async def _generate_diagnostic_insights(self, query: str, symptoms: List[str], 
                                          categorized_symptoms: Dict, severity: Dict) -> str:
        """Generate diagnostic insights using AI"""
        try:
            prompt = f"""
            As a neurologist specializing in COVID-19 neurological manifestations, provide diagnostic insights for:
            
            Query: {query}
            Identified Symptoms: {', '.join(symptoms)}
            Symptom Categories: {json.dumps(categorized_symptoms, indent=2)}
            Severity Assessment: {json.dumps(severity, indent=2)}
            
            Provide concise diagnostic insights focusing on:
            1. Clinical significance of symptom pattern
            2. Potential underlying mechanisms
            3. Diagnostic considerations
            4. Next steps in evaluation
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.2, "num_predict": 512}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating diagnostic insights: {e}")
            return "Error generating diagnostic insights"
    
    def _parse_differential_diagnosis(self, text: str) -> Dict[str, Any]:
        """Parse differential diagnosis text into structured format"""
        # Simple parsing - in production, this could be more sophisticated
        return {
            "primary_considerations": ["Neurocovid", "Post-viral syndrome", "Encephalitis"],
            "secondary_considerations": ["Metabolic encephalopathy", "Drug-induced symptoms"],
            "raw_text": text
        }
    
    async def _generate_comprehensive_report(self, query: str, symptom_analysis: Dict,
                                           differential: Dict, risk_assessment: Dict,
                                           document_results: Dict) -> str:
        """Generate comprehensive diagnostic report"""
        try:
            prompt = f"""
            Generate a comprehensive diagnostic report for the following case:
            
            Query: {query}
            Symptom Analysis: {json.dumps(symptom_analysis, indent=2)}
            Differential Diagnosis: {json.dumps(differential, indent=2)}
            Risk Assessment: {json.dumps(risk_assessment, indent=2)}
            
            Provide a structured medical report with:
            1. Clinical Summary
            2. Diagnostic Impression
            3. Risk Stratification
            4. Recommendations
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.2, "num_predict": 1024}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating comprehensive report: {e}")
            return "Error generating comprehensive report"
    
    def _calculate_analysis_confidence(self, symptom_analysis: Dict, 
                                     differential: Dict, risk_assessment: Dict) -> str:
        """Calculate overall confidence in the analysis"""
        confidence_factors = 0
        
        if symptom_analysis.get("symptoms_identified"):
            confidence_factors += 1
        if differential.get("differential_diagnosis"):
            confidence_factors += 1
        if risk_assessment.get("overall_risk"):
            confidence_factors += 1
        
        if confidence_factors >= 3:
            return "high"
        elif confidence_factors >= 2:
            return "moderate"
        else:
            return "low"
    
    async def _handle_request(self, message: Message):
        """Handle incoming request"""
        try:
            result = await self.process_request(message.content)

            response = Message(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.RESPONSE,
                content=result,
                correlation_id=message.correlation_id
            )

            await self.send_message(response)

        except Exception as e:
            self.logger.error(f"Error handling request: {e}")
