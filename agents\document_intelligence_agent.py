"""
Document Intelligence Agent - Advanced document processing and semantic search
"""
import asyncio
import logging
import numpy as np
from typing import Dict, Any, List, Optional
from pathlib import Path
import chromadb
from sentence_transformers import SentenceTransformer
import ollama

from .base_agent import BaseAgent, Message, MessageType
from config.settings import VECTOR_DB_CONFIG, OLLAMA_CONFIG

logger = logging.getLogger(__name__)


class DocumentIntelligenceAgent(BaseAgent):
    """Agent specialized in document processing and semantic search"""
    
    def __init__(self):
        super().__init__(
            agent_id="document_intelligence",
            name="Document Intelligence Agent",
            role="Advanced document processing and semantic search specialist",
            capabilities=[
                "semantic_search",
                "document_embedding",
                "text_extraction",
                "document_classification",
                "content_summarization"
            ]
        )
        
        self.embedding_model = None
        self.chroma_client = None
        self.collection = None
        self.ollama_client = None
        
        # Initialize components
        asyncio.create_task(self._initialize_components())
        
        # Register message handlers
        self.message_handlers[MessageType.REQUEST] = self._handle_request
    
    async def _initialize_components(self):
        """Initialize embedding model, vector database, and Ollama client"""
        try:
            # Initialize embedding model
            self.logger.info("Loading embedding model...")
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Initialize ChromaDB
            self.logger.info("Initializing ChromaDB...")
            self.chroma_client = chromadb.PersistentClient(
                path=VECTOR_DB_CONFIG["persist_directory"]
            )
            
            # Get or create collection
            try:
                self.collection = self.chroma_client.get_collection(
                    name=VECTOR_DB_CONFIG["collection_name"]
                )
                self.logger.info("Loaded existing ChromaDB collection")
            except:
                self.collection = self.chroma_client.create_collection(
                    name=VECTOR_DB_CONFIG["collection_name"],
                    metadata={"description": "Neurocovid medical documents"}
                )
                self.logger.info("Created new ChromaDB collection")
            
            # Initialize Ollama client
            self.ollama_client = ollama.Client(host=OLLAMA_CONFIG["base_url"])
            
            self.logger.info("Document Intelligence Agent initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing Document Intelligence Agent: {e}")
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming request"""
        action = request.get("action", "")
        inputs = request.get("inputs", {})
        
        if action == "semantic_search":
            return await self._semantic_search(inputs)
        elif action == "add_documents":
            return await self._add_documents(inputs)
        elif action == "summarize_content":
            return await self._summarize_content(inputs)
        elif action == "classify_document":
            return await self._classify_document(inputs)
        else:
            return {"error": f"Unknown action: {action}"}
    
    async def _semantic_search(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform semantic search on documents"""
        try:
            query = inputs.get("query", "")
            max_results = inputs.get("max_results", 5)
            
            if not query:
                return {"error": "Query is required"}
            
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query])[0].tolist()
            
            # Search in ChromaDB
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=max_results,
                include=["documents", "metadatas", "distances"]
            )
            
            # Process results
            documents = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    metadata = results["metadatas"][0][i] if results["metadatas"] else {}
                    distance = results["distances"][0][i] if results["distances"] else 0
                    
                    documents.append({
                        "content": doc,
                        "metadata": metadata,
                        "relevance_score": 1 - distance,  # Convert distance to relevance
                        "filename": metadata.get("filename", "unknown"),
                        "page": metadata.get("page", 0)
                    })
            
            # Generate summary if documents found
            summary = ""
            if documents:
                summary = await self._generate_search_summary(query, documents)
            
            return {
                "query": query,
                "documents": documents,
                "summary": summary,
                "total_results": len(documents)
            }
            
        except Exception as e:
            self.logger.error(f"Error in semantic search: {e}")
            return {"error": str(e)}
    
    async def _add_documents(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Add documents to the vector database"""
        try:
            documents = inputs.get("documents", [])
            
            if not documents:
                return {"error": "No documents provided"}
            
            # Process documents
            texts = []
            metadatas = []
            ids = []
            
            for i, doc in enumerate(documents):
                text = doc.get("text", "")
                metadata = {
                    "filename": doc.get("filename", "unknown"),
                    "page": doc.get("page", 0),
                    "chunk_id": doc.get("chunk_id", f"chunk_{i}"),
                    "added_timestamp": str(asyncio.get_event_loop().time())
                }
                
                texts.append(text)
                metadatas.append(metadata)
                ids.append(metadata["chunk_id"])
            
            # Generate embeddings
            embeddings = self.embedding_model.encode(texts).tolist()
            
            # Add to ChromaDB
            self.collection.add(
                documents=texts,
                metadatas=metadatas,
                embeddings=embeddings,
                ids=ids
            )
            
            self.logger.info(f"Added {len(documents)} documents to vector database")
            
            return {
                "status": "success",
                "documents_added": len(documents),
                "message": f"Successfully added {len(documents)} documents"
            }
            
        except Exception as e:
            self.logger.error(f"Error adding documents: {e}")
            return {"error": str(e)}
    
    async def _summarize_content(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Summarize document content"""
        try:
            content = inputs.get("content", "")
            summary_type = inputs.get("summary_type", "general")
            
            if not content:
                return {"error": "Content is required"}
            
            # Create summary prompt based on type
            if summary_type == "medical":
                prompt = f"""
                Summarize the following medical content focusing on:
                - Key medical findings
                - Symptoms and manifestations
                - Treatment approaches
                - Clinical significance
                
                Content: {content}
                
                Medical Summary:
                """
            else:
                prompt = f"""
                Provide a concise summary of the following content:
                
                {content}
                
                Summary:
                """
            
            # Generate summary using Ollama
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.3, "num_predict": 512}
            )
            
            summary = response["message"]["content"]
            
            return {
                "original_length": len(content),
                "summary": summary,
                "summary_length": len(summary),
                "compression_ratio": len(summary) / len(content)
            }
            
        except Exception as e:
            self.logger.error(f"Error summarizing content: {e}")
            return {"error": str(e)}
    
    async def _classify_document(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Classify document type and medical relevance"""
        try:
            content = inputs.get("content", "")
            
            if not content:
                return {"error": "Content is required"}
            
            # Medical classification keywords
            medical_categories = {
                "neurology": ["neurological", "brain", "nervous system", "neuron", "cognitive"],
                "covid": ["covid", "sars-cov-2", "coronavirus", "pandemic"],
                "symptoms": ["symptom", "manifestation", "clinical", "patient"],
                "treatment": ["treatment", "therapy", "medication", "intervention"],
                "research": ["study", "research", "analysis", "findings", "results"]
            }
            
            # Calculate relevance scores
            content_lower = content.lower()
            scores = {}
            
            for category, keywords in medical_categories.items():
                score = sum(content_lower.count(keyword) for keyword in keywords)
                scores[category] = score
            
            # Determine primary category
            primary_category = max(scores, key=scores.get) if scores else "general"
            
            # Calculate overall medical relevance
            total_medical_keywords = sum(scores.values())
            medical_relevance = min(total_medical_keywords / 10, 1.0)  # Normalize to 0-1
            
            return {
                "primary_category": primary_category,
                "category_scores": scores,
                "medical_relevance": medical_relevance,
                "is_medical": medical_relevance > 0.3
            }
            
        except Exception as e:
            self.logger.error(f"Error classifying document: {e}")
            return {"error": str(e)}
    
    async def _generate_search_summary(self, query: str, documents: List[Dict]) -> str:
        """Generate a summary of search results"""
        try:
            # Combine top documents
            combined_content = "\n\n".join([
                f"[{doc['filename']}, page {doc['page']}]: {doc['content'][:500]}..."
                for doc in documents[:3]
            ])
            
            prompt = f"""
            Based on the following medical documents, provide a comprehensive answer to the query: "{query}"
            
            Documents:
            {combined_content}
            
            Please provide a detailed medical response with proper citations.
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.2, "num_predict": 1024}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating search summary: {e}")
            return "Error generating summary"
    
    async def _handle_request(self, message: Message):
        """Handle incoming request"""
        try:
            result = await self.process_request(message.content)
            
            response = Message(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.RESPONSE,
                content=result,
                correlation_id=message.correlation_id
            )
            
            await self.send_message(response)
            
        except Exception as e:
            self.logger.error(f"Error handling request: {e}")
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the document collection"""
        try:
            if self.collection:
                count = self.collection.count()
                return {
                    "total_documents": count,
                    "collection_name": VECTOR_DB_CONFIG["collection_name"]
                }
            return {"error": "Collection not initialized"}
        except Exception as e:
            return {"error": str(e)}
