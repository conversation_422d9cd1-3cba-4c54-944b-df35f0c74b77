"""
Coordinator Agent - Orchestrates multi-agent workflows
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, Message, MessageType
from .message_bus import message_bus

logger = logging.getLogger(__name__)


class WorkflowStep:
    """Represents a step in a medical workflow"""
    
    def __init__(self, agent_id: str, action: str, inputs: Dict[str, Any], 
                 dependencies: List[str] = None):
        self.agent_id = agent_id
        self.action = action
        self.inputs = inputs
        self.dependencies = dependencies or []
        self.completed = False
        self.result = None
        self.error = None


class MedicalWorkflow:
    """Represents a complete medical analysis workflow"""
    
    def __init__(self, workflow_id: str, query: str, workflow_type: str):
        self.workflow_id = workflow_id
        self.query = query
        self.workflow_type = workflow_type
        self.steps: List[WorkflowStep] = []
        self.status = "pending"
        self.start_time = None
        self.end_time = None
        self.results = {}
        
    def add_step(self, step: WorkflowStep):
        """Add a step to the workflow"""
        self.steps.append(step)
    
    def get_ready_steps(self) -> List[WorkflowStep]:
        """Get steps that are ready to execute (dependencies completed)"""
        ready_steps = []
        for step in self.steps:
            if not step.completed and not step.error:
                # Check if all dependencies are completed
                dependencies_met = all(
                    any(s.agent_id == dep and s.completed for s in self.steps)
                    for dep in step.dependencies
                )
                if dependencies_met:
                    ready_steps.append(step)
        return ready_steps
    
    def is_complete(self) -> bool:
        """Check if workflow is complete"""
        return all(step.completed or step.error for step in self.steps)


class CoordinatorAgent(BaseAgent):
    """Coordinator agent that orchestrates medical workflows"""
    
    def __init__(self):
        super().__init__(
            agent_id="coordinator",
            name="Medical Workflow Coordinator",
            role="Orchestrates multi-agent medical analysis workflows",
            capabilities=[
                "workflow_orchestration",
                "agent_coordination", 
                "query_analysis",
                "result_synthesis"
            ]
        )
        
        self.active_workflows: Dict[str, MedicalWorkflow] = {}
        self.workflow_templates = self._initialize_workflow_templates()
        
        # Register message handlers
        self.message_handlers[MessageType.REQUEST] = self._handle_request
        self.message_handlers[MessageType.RESPONSE] = self._handle_response
    
    def _initialize_workflow_templates(self) -> Dict[str, List[Dict]]:
        """Initialize predefined workflow templates"""
        return {
            "comprehensive_analysis": [
                {
                    "agent_id": "document_intelligence",
                    "action": "semantic_search",
                    "dependencies": []
                },
                {
                    "agent_id": "diagnostic_analysis", 
                    "action": "analyze_symptoms",
                    "dependencies": ["document_intelligence"]
                },
                {
                    "agent_id": "treatment_recommendation",
                    "action": "suggest_treatments", 
                    "dependencies": ["diagnostic_analysis"]
                },
                {
                    "agent_id": "research_synthesis",
                    "action": "synthesize_evidence",
                    "dependencies": ["document_intelligence", "diagnostic_analysis"]
                },
                {
                    "agent_id": "quality_assurance",
                    "action": "validate_recommendations",
                    "dependencies": ["treatment_recommendation", "research_synthesis"]
                }
            ],
            "quick_lookup": [
                {
                    "agent_id": "document_intelligence",
                    "action": "semantic_search",
                    "dependencies": []
                },
                {
                    "agent_id": "research_synthesis",
                    "action": "quick_summary",
                    "dependencies": ["document_intelligence"]
                }
            ],
            "diagnostic_focus": [
                {
                    "agent_id": "document_intelligence", 
                    "action": "semantic_search",
                    "dependencies": []
                },
                {
                    "agent_id": "diagnostic_analysis",
                    "action": "detailed_analysis",
                    "dependencies": ["document_intelligence"]
                },
                {
                    "agent_id": "quality_assurance",
                    "action": "validate_diagnosis",
                    "dependencies": ["diagnostic_analysis"]
                }
            ]
        }
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming request and orchestrate workflow"""
        query = request.get("query", "")
        workflow_type = request.get("workflow_type", "comprehensive_analysis")
        
        # Create workflow
        workflow_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        workflow = await self._create_workflow(workflow_id, query, workflow_type)
        
        if workflow:
            # Execute workflow
            result = await self._execute_workflow(workflow)
            return {
                "workflow_id": workflow_id,
                "status": "completed",
                "result": result
            }
        else:
            return {
                "status": "error",
                "error": "Failed to create workflow"
            }
    
    async def _create_workflow(self, workflow_id: str, query: str, 
                              workflow_type: str) -> Optional[MedicalWorkflow]:
        """Create a new workflow based on template"""
        try:
            workflow = MedicalWorkflow(workflow_id, query, workflow_type)
            
            # Get template
            template = self.workflow_templates.get(workflow_type)
            if not template:
                self.logger.error(f"Unknown workflow type: {workflow_type}")
                return None
            
            # Create workflow steps
            for step_config in template:
                step = WorkflowStep(
                    agent_id=step_config["agent_id"],
                    action=step_config["action"],
                    inputs={"query": query},
                    dependencies=step_config.get("dependencies", [])
                )
                workflow.add_step(step)
            
            self.active_workflows[workflow_id] = workflow
            self.logger.info(f"Created workflow {workflow_id} of type {workflow_type}")
            return workflow
            
        except Exception as e:
            self.logger.error(f"Error creating workflow: {e}")
            return None
    
    async def _execute_workflow(self, workflow: MedicalWorkflow) -> Dict[str, Any]:
        """Execute a workflow by coordinating agents"""
        workflow.status = "running"
        workflow.start_time = datetime.now()
        
        try:
            while not workflow.is_complete():
                # Get ready steps
                ready_steps = workflow.get_ready_steps()
                
                if not ready_steps:
                    # No ready steps, wait a bit
                    await asyncio.sleep(0.5)
                    continue
                
                # Execute ready steps in parallel
                tasks = []
                for step in ready_steps:
                    task = asyncio.create_task(self._execute_step(workflow, step))
                    tasks.append(task)
                
                # Wait for all tasks to complete
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)
            
            workflow.status = "completed"
            workflow.end_time = datetime.now()
            
            # Compile final results
            final_result = await self._compile_workflow_results(workflow)
            return final_result
            
        except Exception as e:
            workflow.status = "error"
            workflow.end_time = datetime.now()
            self.logger.error(f"Error executing workflow {workflow.workflow_id}: {e}")
            return {"error": str(e)}
    
    async def _execute_step(self, workflow: MedicalWorkflow, step: WorkflowStep):
        """Execute a single workflow step"""
        try:
            # Prepare inputs with results from dependencies
            inputs = step.inputs.copy()
            for dep in step.dependencies:
                dep_step = next((s for s in workflow.steps if s.agent_id == dep), None)
                if dep_step and dep_step.result:
                    inputs[f"{dep}_result"] = dep_step.result
            
            # Send request to agent
            request_content = {
                "action": step.action,
                "inputs": inputs,
                "workflow_id": workflow.workflow_id
            }
            
            response = await message_bus.send_request(
                sender_id=self.agent_id,
                receiver_id=step.agent_id,
                content=request_content,
                timeout=60.0
            )
            
            if response and response.message_type == MessageType.RESPONSE:
                step.result = response.content
                step.completed = True
                self.logger.info(f"Step {step.agent_id}:{step.action} completed")
            else:
                step.error = "No response or error response received"
                self.logger.error(f"Step {step.agent_id}:{step.action} failed")
                
        except Exception as e:
            step.error = str(e)
            self.logger.error(f"Error executing step {step.agent_id}:{step.action}: {e}")
    
    async def _compile_workflow_results(self, workflow: MedicalWorkflow) -> Dict[str, Any]:
        """Compile final results from all workflow steps"""
        results = {
            "workflow_id": workflow.workflow_id,
            "query": workflow.query,
            "workflow_type": workflow.workflow_type,
            "status": workflow.status,
            "execution_time": (workflow.end_time - workflow.start_time).total_seconds(),
            "steps_results": {},
            "final_answer": ""
        }
        
        # Collect results from each step
        for step in workflow.steps:
            if step.completed and step.result:
                results["steps_results"][f"{step.agent_id}_{step.action}"] = step.result
        
        # Generate final synthesized answer
        synthesis_result = results["steps_results"].get("research_synthesis_synthesize_evidence")
        if synthesis_result:
            results["final_answer"] = synthesis_result.get("synthesized_response", "")
        else:
            # Fallback to document intelligence result
            doc_result = results["steps_results"].get("document_intelligence_semantic_search")
            if doc_result:
                results["final_answer"] = doc_result.get("summary", "")
        
        return results
    
    async def _handle_request(self, message: Message):
        """Handle incoming request"""
        try:
            result = await self.process_request(message.content)
            
            response = Message(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.RESPONSE,
                content=result,
                correlation_id=message.correlation_id
            )
            
            await self.send_message(response)
            
        except Exception as e:
            self.logger.error(f"Error handling request: {e}")
    
    async def _handle_response(self, message: Message):
        """Handle response from other agents"""
        # Responses are handled in the workflow execution logic
        pass
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific workflow"""
        workflow = self.active_workflows.get(workflow_id)
        if workflow:
            return {
                "workflow_id": workflow_id,
                "status": workflow.status,
                "query": workflow.query,
                "workflow_type": workflow.workflow_type,
                "steps_completed": sum(1 for step in workflow.steps if step.completed),
                "total_steps": len(workflow.steps),
                "start_time": workflow.start_time.isoformat() if workflow.start_time else None,
                "end_time": workflow.end_time.isoformat() if workflow.end_time else None
            }
        return None
