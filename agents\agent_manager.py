"""
Agent Manager - Manages the multi-agent system lifecycle
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_agent import BaseAgent
from .message_bus import message_bus
from .coordinator_agent import CoordinatorAgent
from .document_intelligence_agent import DocumentIntelligenceAgent
from .diagnostic_agent import DiagnosticAnalysisAgent
from .treatment_agent import TreatmentRecommendationAgent
from .research_synthesis_agent import ResearchSynthesisAgent
from .quality_assurance_agent import QualityAssuranceAgent

logger = logging.getLogger(__name__)


class AgentManager:
    """Manages the lifecycle and coordination of all agents"""
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.running = False
        self.start_time = None
        
        # Performance metrics
        self.total_requests_processed = 0
        self.total_errors = 0
        
    async def initialize(self):
        """Initialize all agents and the message bus"""
        try:
            logger.info("Initializing multi-agent system...")
            
            # Start message bus
            await message_bus.start()
            
            # Create and register agents
            await self._create_agents()
            
            # Start all agents
            await self._start_agents()
            
            self.running = True
            self.start_time = datetime.now()
            
            logger.info("Multi-agent system initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing agent system: {e}")
            raise
    
    async def _create_agents(self):
        """Create all specialized agents"""
        try:
            # Create coordinator agent
            coordinator = CoordinatorAgent()
            self.agents[coordinator.agent_id] = coordinator
            message_bus.register_agent(coordinator)
            
            # Create document intelligence agent
            doc_agent = DocumentIntelligenceAgent()
            self.agents[doc_agent.agent_id] = doc_agent
            message_bus.register_agent(doc_agent)
            
            # Create diagnostic analysis agent
            diagnostic_agent = DiagnosticAnalysisAgent()
            self.agents[diagnostic_agent.agent_id] = diagnostic_agent
            message_bus.register_agent(diagnostic_agent)
            
            # Create treatment recommendation agent
            treatment_agent = TreatmentRecommendationAgent()
            self.agents[treatment_agent.agent_id] = treatment_agent
            message_bus.register_agent(treatment_agent)
            
            # Create research synthesis agent
            research_agent = ResearchSynthesisAgent()
            self.agents[research_agent.agent_id] = research_agent
            message_bus.register_agent(research_agent)
            
            # Create quality assurance agent
            qa_agent = QualityAssuranceAgent()
            self.agents[qa_agent.agent_id] = qa_agent
            message_bus.register_agent(qa_agent)
            
            logger.info(f"Created {len(self.agents)} agents")
            
        except Exception as e:
            logger.error(f"Error creating agents: {e}")
            raise
    
    async def _start_agents(self):
        """Start all agents"""
        try:
            for agent in self.agents.values():
                await agent.start()
            
            logger.info("All agents started successfully")
            
        except Exception as e:
            logger.error(f"Error starting agents: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the multi-agent system"""
        try:
            logger.info("Shutting down multi-agent system...")
            
            # Stop all agents
            for agent in self.agents.values():
                await agent.stop()
            
            # Stop message bus
            await message_bus.stop()
            
            self.running = False
            
            logger.info("Multi-agent system shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def process_query(self, query: str, workflow_type: str = "comprehensive_analysis",
                           patient_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process a medical query through the multi-agent system"""
        try:
            if not self.running:
                return {"error": "Agent system not running"}
            
            # Get coordinator agent
            coordinator = self.agents.get("coordinator")
            if not coordinator:
                return {"error": "Coordinator agent not available"}
            
            # Prepare request
            request_content = {
                "query": query,
                "workflow_type": workflow_type,
                "patient_context": patient_context or {},
                "timestamp": datetime.now().isoformat()
            }
            
            # Send request to coordinator
            result = await coordinator.process_request(request_content)
            
            self.total_requests_processed += 1
            
            return result
            
        except Exception as e:
            self.total_errors += 1
            logger.error(f"Error processing query: {e}")
            return {"error": str(e)}
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Add documents to the system"""
        try:
            # Get document intelligence agent
            doc_agent = self.agents.get("document_intelligence")
            if not doc_agent:
                return {"error": "Document intelligence agent not available"}
            
            # Prepare request
            request_content = {
                "action": "add_documents",
                "inputs": {"documents": documents}
            }
            
            # Process request
            result = await doc_agent.process_request(request_content)
            
            return result
            
        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            return {"error": str(e)}
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents"""
        agent_statuses = {}
        
        for agent_id, agent in self.agents.items():
            agent_statuses[agent_id] = agent.get_info()
        
        return {
            "system_running": self.running,
            "system_uptime": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
            "total_agents": len(self.agents),
            "total_requests_processed": self.total_requests_processed,
            "total_errors": self.total_errors,
            "agents": agent_statuses,
            "message_bus_stats": message_bus.get_statistics()
        }
    
    def get_workflow_templates(self) -> Dict[str, Any]:
        """Get available workflow templates"""
        coordinator = self.agents.get("coordinator")
        if coordinator and hasattr(coordinator, 'workflow_templates'):
            return {
                "available_workflows": list(coordinator.workflow_templates.keys()),
                "workflow_descriptions": {
                    "comprehensive_analysis": "Full multi-agent analysis including diagnosis, treatment, and synthesis",
                    "quick_lookup": "Fast document search and summary",
                    "diagnostic_focus": "Focused diagnostic analysis with validation"
                }
            }
        return {"error": "Coordinator not available"}
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific workflow"""
        coordinator = self.agents.get("coordinator")
        if coordinator and hasattr(coordinator, 'get_workflow_status'):
            return coordinator.get_workflow_status(workflow_id)
        return None
    
    def get_agent_capabilities(self) -> Dict[str, List[str]]:
        """Get capabilities of all agents"""
        capabilities = {}
        
        for agent_id, agent in self.agents.items():
            capabilities[agent_id] = {
                "name": agent.name,
                "role": agent.role,
                "capabilities": agent.capabilities
            }
        
        return capabilities
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform system health check"""
        health_status = {
            "system_healthy": True,
            "timestamp": datetime.now().isoformat(),
            "checks": {}
        }
        
        try:
            # Check message bus
            health_status["checks"]["message_bus"] = {
                "status": "healthy" if message_bus.running else "unhealthy",
                "agents_registered": len(message_bus.agents)
            }
            
            # Check each agent
            for agent_id, agent in self.agents.items():
                agent_healthy = (
                    agent.running and 
                    agent.status.value != "error" and
                    agent.errors_count < 10
                )
                
                health_status["checks"][agent_id] = {
                    "status": "healthy" if agent_healthy else "unhealthy",
                    "running": agent.running,
                    "agent_status": agent.status.value,
                    "errors_count": agent.errors_count,
                    "messages_processed": agent.messages_processed
                }
                
                if not agent_healthy:
                    health_status["system_healthy"] = False
            
            # Overall system health
            if not message_bus.running or not self.running:
                health_status["system_healthy"] = False
            
        except Exception as e:
            health_status["system_healthy"] = False
            health_status["error"] = str(e)
        
        return health_status
    
    async def restart_agent(self, agent_id: str) -> Dict[str, Any]:
        """Restart a specific agent"""
        try:
            agent = self.agents.get(agent_id)
            if not agent:
                return {"error": f"Agent {agent_id} not found"}
            
            # Stop agent
            await agent.stop()
            
            # Wait a moment
            await asyncio.sleep(1)
            
            # Start agent
            await agent.start()
            
            return {"status": "success", "message": f"Agent {agent_id} restarted"}
            
        except Exception as e:
            logger.error(f"Error restarting agent {agent_id}: {e}")
            return {"error": str(e)}
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get comprehensive system metrics"""
        metrics = {
            "system_metrics": {
                "uptime_seconds": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
                "total_requests": self.total_requests_processed,
                "total_errors": self.total_errors,
                "error_rate": self.total_errors / max(self.total_requests_processed, 1),
                "agents_count": len(self.agents)
            },
            "agent_metrics": {},
            "message_bus_metrics": message_bus.get_statistics()
        }
        
        # Collect agent metrics
        for agent_id, agent in self.agents.items():
            metrics["agent_metrics"][agent_id] = {
                "messages_processed": agent.messages_processed,
                "errors_count": agent.errors_count,
                "uptime_seconds": (datetime.now() - agent.start_time).total_seconds() if agent.start_time else 0,
                "status": agent.status.value
            }
        
        return metrics


# Global agent manager instance
agent_manager = AgentManager()
