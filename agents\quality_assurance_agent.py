"""
Quality Assurance Agent - Medical accuracy and safety validation
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
import ollama
import json
import re

from .base_agent import BaseAgent, Message, MessageType
from config.settings import OLLAMA_CONFIG

logger = logging.getLogger(__name__)


class QualityAssuranceAgent(BaseAgent):
    """Agent specialized in medical accuracy and safety validation"""
    
    def __init__(self):
        super().__init__(
            agent_id="quality_assurance",
            name="Quality Assurance Agent",
            role="Medical accuracy and safety validation specialist",
            capabilities=[
                "medical_fact_checking",
                "safety_validation",
                "guideline_compliance",
                "risk_assessment",
                "quality_scoring"
            ]
        )
        
        self.ollama_client = None
        self.validation_rules = self._initialize_validation_rules()
        self.safety_checks = self._initialize_safety_checks()
        
        # Initialize Ollama client
        asyncio.create_task(self._initialize_ollama())
        
        # Register message handlers
        self.message_handlers[MessageType.REQUEST] = self._handle_request
    
    async def _initialize_ollama(self):
        """Initialize Ollama client"""
        try:
            self.ollama_client = ollama.Client(host=OLLAMA_CONFIG["base_url"])
            self.logger.info("Quality Assurance Agent initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing Ollama client: {e}")
    
    def _initialize_validation_rules(self) -> Dict[str, Any]:
        """Initialize medical validation rules"""
        return {
            "contraindications": {
                "corticosteroids": [
                    "active_systemic_infection",
                    "live_vaccine_administration",
                    "hypersensitivity"
                ],
                "nsaids": [
                    "severe_kidney_disease",
                    "active_peptic_ulcer",
                    "severe_heart_failure"
                ],
                "antiepileptics": [
                    "known_hypersensitivity",
                    "severe_liver_disease",
                    "bone_marrow_suppression"
                ]
            },
            "drug_interactions": {
                "high_risk": [
                    "warfarin_antibiotics",
                    "digoxin_diuretics",
                    "lithium_nsaids"
                ],
                "moderate_risk": [
                    "antiepileptics_oral_contraceptives",
                    "corticosteroids_diabetes_medications"
                ]
            },
            "dosage_limits": {
                "acetaminophen": {"max_daily": "4000mg", "max_single": "1000mg"},
                "ibuprofen": {"max_daily": "2400mg", "max_single": "800mg"},
                "prednisone": {"max_daily": "80mg", "taper_required": True}
            },
            "monitoring_requirements": {
                "corticosteroids": ["blood_glucose", "blood_pressure", "bone_density"],
                "antiepileptics": ["liver_function", "blood_counts", "drug_levels"],
                "immunosuppressants": ["infection_monitoring", "blood_counts"]
            }
        }
    
    def _initialize_safety_checks(self) -> Dict[str, Any]:
        """Initialize safety check protocols"""
        return {
            "red_flags": [
                "sudden_onset_severe_headache",
                "altered_consciousness",
                "focal_neurological_deficits",
                "seizures",
                "signs_of_increased_icp"
            ],
            "emergency_criteria": [
                "glasgow_coma_scale_less_than_13",
                "respiratory_distress",
                "hemodynamic_instability",
                "status_epilepticus"
            ],
            "contraindication_alerts": [
                "pregnancy_category_x_drugs",
                "allergy_documented",
                "organ_failure_contraindications"
            ],
            "age_specific_considerations": {
                "pediatric": ["weight_based_dosing", "developmental_considerations"],
                "geriatric": ["reduced_clearance", "polypharmacy_risks"],
                "pregnancy": ["teratogenic_risks", "category_classification"]
            }
        }
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming quality assurance request"""
        action = request.get("action", "")
        inputs = request.get("inputs", {})
        
        if action == "validate_recommendations":
            return await self._validate_recommendations(inputs)
        elif action == "validate_diagnosis":
            return await self._validate_diagnosis(inputs)
        elif action == "safety_check":
            return await self._safety_check(inputs)
        elif action == "guideline_compliance":
            return await self._check_guideline_compliance(inputs)
        else:
            return {"error": f"Unknown action: {action}"}
    
    async def _validate_recommendations(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Validate treatment recommendations for safety and accuracy"""
        try:
            treatment_results = inputs.get("treatment_recommendation_result", {})
            diagnostic_results = inputs.get("diagnostic_analysis_result", {})
            patient_context = inputs.get("patient_context", {})
            
            # Extract recommendations to validate
            recommendations = self._extract_recommendations(treatment_results)
            
            # Perform validation checks
            safety_issues = self._check_safety_issues(recommendations, patient_context)
            contraindication_warnings = self._check_contraindications(recommendations, patient_context)
            interaction_warnings = self._check_drug_interactions(recommendations)
            dosage_warnings = self._check_dosage_appropriateness(recommendations)
            
            # Generate overall safety score
            safety_score = self._calculate_safety_score(
                safety_issues, contraindication_warnings, interaction_warnings, dosage_warnings
            )
            
            # Generate validation report
            validation_report = await self._generate_validation_report(
                recommendations, safety_issues, contraindication_warnings, 
                interaction_warnings, dosage_warnings, safety_score
            )
            
            return {
                "validation_status": "passed" if safety_score >= 7 else "requires_review",
                "safety_score": safety_score,
                "safety_issues": safety_issues,
                "contraindication_warnings": contraindication_warnings,
                "interaction_warnings": interaction_warnings,
                "dosage_warnings": dosage_warnings,
                "validation_report": validation_report,
                "recommendations_approved": safety_score >= 7
            }
            
        except Exception as e:
            self.logger.error(f"Error validating recommendations: {e}")
            return {"error": str(e)}
    
    async def _validate_diagnosis(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Validate diagnostic conclusions"""
        try:
            diagnostic_results = inputs.get("diagnostic_analysis_result", {})
            document_evidence = inputs.get("document_intelligence_result", {})
            
            # Extract diagnostic conclusions
            diagnostic_conclusions = self._extract_diagnostic_conclusions(diagnostic_results)
            
            # Validate against evidence
            evidence_support = self._assess_evidence_support(diagnostic_conclusions, document_evidence)
            
            # Check for diagnostic red flags
            red_flag_assessment = self._check_diagnostic_red_flags(diagnostic_conclusions)
            
            # Validate diagnostic criteria
            criteria_validation = self._validate_diagnostic_criteria(diagnostic_conclusions)
            
            # Generate diagnostic confidence score
            confidence_score = self._calculate_diagnostic_confidence(
                evidence_support, red_flag_assessment, criteria_validation
            )
            
            # Generate diagnostic validation report
            diagnostic_report = await self._generate_diagnostic_validation_report(
                diagnostic_conclusions, evidence_support, red_flag_assessment, 
                criteria_validation, confidence_score
            )
            
            return {
                "diagnostic_validation": "approved" if confidence_score >= 7 else "needs_review",
                "confidence_score": confidence_score,
                "evidence_support": evidence_support,
                "red_flag_assessment": red_flag_assessment,
                "criteria_validation": criteria_validation,
                "diagnostic_report": diagnostic_report
            }
            
        except Exception as e:
            self.logger.error(f"Error validating diagnosis: {e}")
            return {"error": str(e)}
    
    async def _safety_check(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive safety check"""
        try:
            all_results = inputs.get("all_agent_results", {})
            patient_context = inputs.get("patient_context", {})
            
            # Comprehensive safety assessment
            emergency_flags = self._check_emergency_criteria(all_results)
            safety_alerts = self._generate_safety_alerts(all_results, patient_context)
            risk_stratification = self._perform_risk_stratification(all_results, patient_context)
            
            # Generate safety recommendations
            safety_recommendations = self._generate_safety_recommendations(
                emergency_flags, safety_alerts, risk_stratification
            )
            
            # Overall safety assessment
            overall_safety = self._assess_overall_safety(
                emergency_flags, safety_alerts, risk_stratification
            )
            
            return {
                "overall_safety": overall_safety,
                "emergency_flags": emergency_flags,
                "safety_alerts": safety_alerts,
                "risk_stratification": risk_stratification,
                "safety_recommendations": safety_recommendations,
                "requires_immediate_attention": len(emergency_flags) > 0
            }
            
        except Exception as e:
            self.logger.error(f"Error in safety check: {e}")
            return {"error": str(e)}
    
    async def _check_guideline_compliance(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Check compliance with medical guidelines"""
        try:
            treatment_results = inputs.get("treatment_recommendation_result", {})
            diagnostic_results = inputs.get("diagnostic_analysis_result", {})
            
            # Check against standard guidelines
            guideline_compliance = await self._assess_guideline_compliance(
                treatment_results, diagnostic_results
            )
            
            # Identify deviations
            deviations = self._identify_guideline_deviations(guideline_compliance)
            
            # Generate compliance report
            compliance_report = await self._generate_compliance_report(
                guideline_compliance, deviations
            )
            
            return {
                "guideline_compliance": guideline_compliance,
                "deviations": deviations,
                "compliance_score": self._calculate_compliance_score(guideline_compliance),
                "compliance_report": compliance_report
            }
            
        except Exception as e:
            self.logger.error(f"Error checking guideline compliance: {e}")
            return {"error": str(e)}
    
    def _extract_recommendations(self, treatment_results: Dict) -> List[Dict]:
        """Extract treatment recommendations for validation"""
        recommendations = []
        
        # Extract from symptomatic treatments
        symptomatic = treatment_results.get("symptomatic_treatments", [])
        recommendations.extend(symptomatic)
        
        # Extract from neuroprotective treatments
        neuroprotective = treatment_results.get("neuroprotective_treatments", [])
        recommendations.extend(neuroprotective)
        
        return recommendations
    
    def _check_safety_issues(self, recommendations: List[Dict], patient_context: Dict) -> List[Dict]:
        """Check for safety issues in recommendations"""
        safety_issues = []
        
        patient_conditions = patient_context.get("conditions", [])
        patient_allergies = patient_context.get("allergies", [])
        
        for rec in recommendations:
            medication = rec.get("medication", "")
            
            # Check allergies
            if medication in patient_allergies:
                safety_issues.append({
                    "type": "allergy",
                    "medication": medication,
                    "severity": "high",
                    "description": f"Patient has documented allergy to {medication}"
                })
            
            # Check condition-specific safety
            for condition in patient_conditions:
                if self._is_unsafe_combination(medication, condition):
                    safety_issues.append({
                        "type": "unsafe_combination",
                        "medication": medication,
                        "condition": condition,
                        "severity": "moderate",
                        "description": f"{medication} may be unsafe with {condition}"
                    })
        
        return safety_issues
    
    def _check_contraindications(self, recommendations: List[Dict], patient_context: Dict) -> List[Dict]:
        """Check for contraindications"""
        warnings = []
        
        patient_conditions = patient_context.get("conditions", [])
        
        for rec in recommendations:
            medication = rec.get("medication", "")
            
            if medication in self.validation_rules["contraindications"]:
                contraindications = self.validation_rules["contraindications"][medication]
                
                for condition in patient_conditions:
                    if condition in contraindications:
                        warnings.append({
                            "medication": medication,
                            "contraindication": condition,
                            "severity": "high",
                            "action": "avoid_or_use_alternative"
                        })
        
        return warnings
    
    def _check_drug_interactions(self, recommendations: List[Dict]) -> List[Dict]:
        """Check for drug interactions"""
        interactions = []
        
        medications = [rec.get("medication", "") for rec in recommendations]
        
        # Check high-risk interactions
        for interaction in self.validation_rules["drug_interactions"]["high_risk"]:
            drugs = interaction.split("_")
            if len(drugs) >= 2 and all(drug in medications for drug in drugs[:2]):
                interactions.append({
                    "type": "high_risk",
                    "medications": drugs[:2],
                    "severity": "high",
                    "action": "avoid_combination"
                })
        
        return interactions
    
    def _check_dosage_appropriateness(self, recommendations: List[Dict]) -> List[Dict]:
        """Check dosage appropriateness"""
        warnings = []
        
        for rec in recommendations:
            medication = rec.get("medication", "")
            dosage = rec.get("dosage", "")
            
            if medication in self.validation_rules["dosage_limits"]:
                limits = self.validation_rules["dosage_limits"][medication]
                
                # Simple dosage checking (in production, this would be more sophisticated)
                if "max_daily" in limits:
                    warnings.append({
                        "medication": medication,
                        "type": "dosage_reminder",
                        "message": f"Maximum daily dose: {limits['max_daily']}",
                        "severity": "low"
                    })
        
        return warnings
    
    def _calculate_safety_score(self, safety_issues: List, contraindications: List, 
                               interactions: List, dosage_warnings: List) -> int:
        """Calculate overall safety score (0-10)"""
        score = 10
        
        # Deduct points for issues
        score -= len(safety_issues) * 2
        score -= len(contraindications) * 3
        score -= len([i for i in interactions if i.get("severity") == "high"]) * 3
        score -= len([i for i in interactions if i.get("severity") == "moderate"]) * 1
        score -= len(dosage_warnings) * 0.5
        
        return max(0, int(score))
    
    def _extract_diagnostic_conclusions(self, diagnostic_results: Dict) -> List[str]:
        """Extract diagnostic conclusions"""
        conclusions = []
        
        # Extract from diagnostic insights
        insights = diagnostic_results.get("diagnostic_insights", "")
        if insights:
            conclusions.append(insights)
        
        # Extract from differential diagnosis
        differential = diagnostic_results.get("differential_diagnosis", {})
        if differential:
            conclusions.append(str(differential))
        
        return conclusions
    
    def _assess_evidence_support(self, conclusions: List[str], evidence: Dict) -> Dict[str, Any]:
        """Assess evidence support for conclusions"""
        documents = evidence.get("documents", [])
        
        support_score = 0
        if len(documents) >= 3:
            support_score = 8
        elif len(documents) >= 2:
            support_score = 6
        elif len(documents) >= 1:
            support_score = 4
        
        return {
            "support_score": support_score,
            "evidence_sources": len(documents),
            "support_level": "strong" if support_score >= 7 else "moderate" if support_score >= 5 else "weak"
        }
    
    def _check_diagnostic_red_flags(self, conclusions: List[str]) -> List[Dict]:
        """Check for diagnostic red flags"""
        red_flags = []
        
        for conclusion in conclusions:
            conclusion_lower = conclusion.lower()
            
            for flag in self.safety_checks["red_flags"]:
                if flag.replace("_", " ") in conclusion_lower:
                    red_flags.append({
                        "flag": flag,
                        "severity": "high",
                        "action": "immediate_evaluation_required"
                    })
        
        return red_flags
    
    def _validate_diagnostic_criteria(self, conclusions: List[str]) -> Dict[str, Any]:
        """Validate diagnostic criteria"""
        # Simplified validation
        return {
            "criteria_met": True,
            "missing_criteria": [],
            "validation_score": 8
        }
    
    def _calculate_diagnostic_confidence(self, evidence_support: Dict, 
                                       red_flags: List, criteria: Dict) -> int:
        """Calculate diagnostic confidence score"""
        base_score = evidence_support.get("support_score", 5)
        
        # Deduct for red flags
        base_score -= len(red_flags) * 2
        
        # Adjust for criteria validation
        if criteria.get("criteria_met", False):
            base_score += 1
        
        return max(0, min(10, base_score))
    
    def _check_emergency_criteria(self, all_results: Dict) -> List[Dict]:
        """Check for emergency criteria"""
        emergency_flags = []
        
        # Check diagnostic results for emergency indicators
        diagnostic_results = all_results.get("diagnostic_analysis", {})
        symptoms = diagnostic_results.get("symptoms_identified", [])
        
        for symptom in symptoms:
            if symptom in ["seizures", "altered_consciousness", "severe_headache"]:
                emergency_flags.append({
                    "type": "emergency_symptom",
                    "symptom": symptom,
                    "action": "immediate_medical_attention"
                })
        
        return emergency_flags
    
    def _generate_safety_alerts(self, all_results: Dict, patient_context: Dict) -> List[Dict]:
        """Generate safety alerts"""
        alerts = []
        
        # Check for high-risk combinations
        treatment_results = all_results.get("treatment_recommendation", {})
        if treatment_results:
            contraindications = treatment_results.get("contraindication_warnings", [])
            for contraindication in contraindications:
                if contraindication.get("severity") == "high":
                    alerts.append({
                        "type": "contraindication_alert",
                        "details": contraindication,
                        "priority": "high"
                    })
        
        return alerts
    
    def _perform_risk_stratification(self, all_results: Dict, patient_context: Dict) -> Dict[str, Any]:
        """Perform risk stratification"""
        # Simplified risk stratification
        risk_factors = patient_context.get("risk_factors", [])
        
        risk_score = len(risk_factors)
        
        if risk_score >= 3:
            risk_level = "high"
        elif risk_score >= 1:
            risk_level = "moderate"
        else:
            risk_level = "low"
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "risk_factors": risk_factors
        }
    
    def _generate_safety_recommendations(self, emergency_flags: List, 
                                       safety_alerts: List, risk_stratification: Dict) -> List[str]:
        """Generate safety recommendations"""
        recommendations = []
        
        if emergency_flags:
            recommendations.append("Immediate medical evaluation required")
        
        if safety_alerts:
            recommendations.append("Review contraindications and drug interactions")
        
        risk_level = risk_stratification.get("risk_level", "low")
        if risk_level == "high":
            recommendations.append("Close monitoring and frequent follow-up required")
        elif risk_level == "moderate":
            recommendations.append("Regular monitoring recommended")
        
        return recommendations
    
    def _assess_overall_safety(self, emergency_flags: List, safety_alerts: List, 
                              risk_stratification: Dict) -> str:
        """Assess overall safety"""
        if emergency_flags:
            return "high_risk"
        elif len(safety_alerts) >= 2:
            return "moderate_risk"
        elif risk_stratification.get("risk_level") == "high":
            return "moderate_risk"
        else:
            return "low_risk"
    
    def _is_unsafe_combination(self, medication: str, condition: str) -> bool:
        """Check if medication-condition combination is unsafe"""
        # Simplified safety checking
        unsafe_combinations = {
            "nsaids": ["kidney_disease", "heart_failure"],
            "corticosteroids": ["diabetes", "infection"],
            "antiepileptics": ["liver_disease"]
        }
        
        return condition in unsafe_combinations.get(medication, [])
    
    async def _generate_validation_report(self, recommendations: List[Dict], 
                                        safety_issues: List, contraindications: List,
                                        interactions: List, dosage_warnings: List, 
                                        safety_score: int) -> str:
        """Generate validation report"""
        try:
            prompt = f"""
            Generate a medical validation report for the following treatment recommendations:
            
            Recommendations: {json.dumps(recommendations, indent=2)}
            Safety Issues: {json.dumps(safety_issues, indent=2)}
            Contraindications: {json.dumps(contraindications, indent=2)}
            Drug Interactions: {json.dumps(interactions, indent=2)}
            Dosage Warnings: {json.dumps(dosage_warnings, indent=2)}
            Safety Score: {safety_score}/10
            
            Provide a structured validation report with:
            1. Overall assessment
            2. Critical safety concerns
            3. Recommendations for improvement
            4. Monitoring requirements
            5. Final approval status
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.1, "num_predict": 1024}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating validation report: {e}")
            return "Error generating validation report"
    
    async def _generate_diagnostic_validation_report(self, conclusions: List[str],
                                                   evidence_support: Dict, red_flags: List,
                                                   criteria_validation: Dict, confidence_score: int) -> str:
        """Generate diagnostic validation report"""
        try:
            prompt = f"""
            Generate a diagnostic validation report:
            
            Diagnostic Conclusions: {json.dumps(conclusions, indent=2)}
            Evidence Support: {json.dumps(evidence_support, indent=2)}
            Red Flags: {json.dumps(red_flags, indent=2)}
            Criteria Validation: {json.dumps(criteria_validation, indent=2)}
            Confidence Score: {confidence_score}/10
            
            Provide assessment of diagnostic accuracy and reliability.
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.1, "num_predict": 512}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating diagnostic validation report: {e}")
            return "Error generating diagnostic validation report"
    
    async def _assess_guideline_compliance(self, treatment_results: Dict, 
                                         diagnostic_results: Dict) -> Dict[str, Any]:
        """Assess compliance with medical guidelines"""
        # Simplified guideline compliance assessment
        return {
            "diagnostic_guidelines": "compliant",
            "treatment_guidelines": "mostly_compliant",
            "evidence_based": True,
            "deviations": []
        }
    
    def _identify_guideline_deviations(self, compliance: Dict) -> List[Dict]:
        """Identify deviations from guidelines"""
        return compliance.get("deviations", [])
    
    def _calculate_compliance_score(self, compliance: Dict) -> int:
        """Calculate compliance score"""
        if compliance.get("evidence_based", False):
            return 8
        else:
            return 5
    
    async def _generate_compliance_report(self, compliance: Dict, deviations: List) -> str:
        """Generate compliance report"""
        try:
            prompt = f"""
            Generate a guideline compliance report:
            
            Compliance Assessment: {json.dumps(compliance, indent=2)}
            Deviations: {json.dumps(deviations, indent=2)}
            
            Assess adherence to medical guidelines and standards.
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.1, "num_predict": 512}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating compliance report: {e}")
            return "Error generating compliance report"
    
    async def _handle_request(self, message: Message):
        """Handle incoming request"""
        try:
            result = await self.process_request(message.content)
            
            response = Message(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.RESPONSE,
                content=result,
                correlation_id=message.correlation_id
            )
            
            await self.send_message(response)
            
        except Exception as e:
            self.logger.error(f"Error handling request: {e}")
