# 📚 Guia Completo: Upload de PDFs Científicos em Massa

Este guia explica como fazer upload de centenas de PDFs científicos para que os agentes possam consultar e citar essas referências.

## 🎯 **Métodos de Upload**

### **Método 1: Upload em Massa via Script (Recomendado para 100+ PDFs)**

#### **Passo 1: Organizar os PDFs**
```bash
# Criar diretório para PDFs
mkdir pdfs_cientificos

# Estrutura recomendada:
pdfs_cientificos/
├── neurocovid/
│   ├── neurocovid_study_2023.pdf
│   ├── anosmia_covid_research.pdf
│   └── ...
├── neurologia/
│   ├── neurological_manifestations.pdf
│   └── ...
└── tratamentos/
    ├── treatment_protocols.pdf
    └── ...
```

#### **Passo 2: Executar o Processador em Massa**
```bash
python bulk_pdf_processor.py
```

**O script irá:**
- ✅ Encontrar todos os PDFs automaticamente
- ✅ Extrair texto de cada página
- ✅ Dividir em chunks otimizados para busca
- ✅ Adicionar ao sistema com metadados completos
- ✅ Gerar relatório detalhado do processamento

#### **Passo 3: Verificar Processamento**
O script gera um relatório `pdf_processing_report.json` com:
- Total de arquivos processados
- Arquivos que falharam
- Número de chunks criados
- Estatísticas detalhadas

### **Método 2: Upload via Interface Web (Para poucos PDFs)**

1. **Abrir a aplicação:**
   ```bash
   streamlit run multi_agent_app.py
   ```

2. **Ir para aba "Document Management"**

3. **Selecionar múltiplos PDFs:**
   - Use Ctrl+Click para selecionar vários
   - Limite: ~50 PDFs por vez

4. **Clicar em "Process Documents"**

## 🔍 **Como os Agentes Usam as Referências**

### **Sistema de Citações Científicas**

Quando você faz uma consulta, os agentes:

1. **Document Intelligence Agent:**
   - Busca semanticamente nos PDFs
   - Encontra os trechos mais relevantes
   - Retorna com metadados (arquivo, página)

2. **Research Synthesis Agent:**
   - Compila evidências de múltiplas fontes
   - Gera citações no formato [1], [2], [3]
   - Inclui lista de referências completa

3. **Quality Assurance Agent:**
   - Valida a qualidade das citações
   - Verifica consistência das referências

### **Exemplo de Resposta com Citações:**

```
Pergunta: "Quais são os tratamentos para anosmia pós-COVID?"

Resposta do Sistema:
A anosmia pós-COVID pode ser tratada através de várias abordagens terapêuticas. 
O treinamento olfatório é considerado o tratamento de primeira linha [1,3,5], 
mostrando eficácia em 60-80% dos pacientes [2]. Corticosteroides podem ser 
considerados em casos agudos [4,7], embora sua eficácia seja controversa [6].

REFERÊNCIAS:
[1] anosmia_treatment_2023.pdf, Página 15
[2] covid_olfactory_study.pdf, Página 8
[3] smell_training_protocol.pdf, Página 3
[4] corticosteroids_anosmia.pdf, Página 12
[5] neurocovid_treatments.pdf, Página 45
[6] systematic_review_anosmia.pdf, Página 23
[7] clinical_guidelines_2024.pdf, Página 67
```

## 📊 **Tipos de PDFs Recomendados**

### **Artigos Científicos:**
- ✅ Estudos clínicos sobre neurocovid
- ✅ Revisões sistemáticas
- ✅ Meta-análises
- ✅ Diretrizes clínicas
- ✅ Protocolos de tratamento

### **Formatos Suportados:**
- ✅ PDFs com texto pesquisável
- ✅ PDFs escaneados (OCR automático)
- ✅ Artigos em português, inglês, espanhol
- ✅ Documentos multi-página

### **Fontes Recomendadas:**
- 🔬 PubMed/PMC
- 🔬 SciELO
- 🔬 Google Scholar
- 🔬 Cochrane Library
- 🔬 Diretrizes de sociedades médicas

## ⚡ **Otimização para Grandes Volumes**

### **Para 100-500 PDFs:**
```bash
# Configuração padrão
python bulk_pdf_processor.py
```

### **Para 500-1000 PDFs:**
```bash
# Processar em lotes menores
# Editar bulk_pdf_processor.py:
batch_size = 5  # Reduzir para 5 PDFs por lote
```

### **Para 1000+ PDFs:**
```bash
# Processar por categorias
mkdir pdfs_cientificos/lote1
mkdir pdfs_cientificos/lote2
# Processar cada lote separadamente
```

## 🔧 **Configurações Avançadas**

### **Personalizar Extração de Texto:**

Edite `bulk_pdf_processor.py`:

```python
# Ajustar tamanho dos chunks
def _split_text_into_chunks(self, text: str, max_length: int = 1500, overlap: int = 150):
    # max_length: tamanho máximo do chunk
    # overlap: sobreposição entre chunks
```

### **Filtrar Tipos de Conteúdo:**

```python
# Adicionar filtros por palavras-chave
medical_keywords = [
    'covid', 'neurocovid', 'anosmia', 'ageusia', 
    'neurológico', 'tratamento', 'diagnóstico'
]
```

## 📈 **Monitoramento do Processamento**

### **Durante o Processamento:**
- 📊 Progresso em tempo real
- 📊 Arquivos processados vs. falharam
- 📊 Chunks criados por arquivo
- 📊 Tempo estimado restante

### **Após o Processamento:**
- 📋 Relatório JSON detalhado
- 📋 Lista de arquivos processados
- 📋 Lista de arquivos que falharam
- 📋 Estatísticas de performance

## 🔍 **Testando as Referências**

### **Consultas de Teste:**

1. **Teste Básico:**
   ```
   "Quais são os sintomas neurológicos da COVID-19?"
   ```

2. **Teste de Tratamento:**
   ```
   "Como tratar anosmia pós-COVID com base na literatura científica?"
   ```

3. **Teste de Diagnóstico:**
   ```
   "Quais são os critérios diagnósticos para neurocovid segundo as diretrizes atuais?"
   ```

### **Verificar Citações:**
- ✅ Citações numeradas [1], [2], [3]
- ✅ Lista de referências completa
- ✅ Páginas específicas citadas
- ✅ Múltiplas fontes por afirmação

## 🚨 **Solução de Problemas**

### **PDFs não processados:**
```bash
# Verificar formato do PDF
file exemplo.pdf

# Tentar conversão manual
pdftk exemplo.pdf output exemplo_fixed.pdf
```

### **Erro de memória:**
```bash
# Reduzir batch_size
batch_size = 3  # Processar menos PDFs por vez
```

### **Texto não extraído:**
```bash
# Verificar se PDF tem texto pesquisável
pdftotext exemplo.pdf -  # Se vazio, precisa OCR
```

## 📚 **Exemplo Completo de Uso**

### **1. Preparar PDFs:**
```bash
mkdir pdfs_cientificos
# Copiar seus PDFs para esta pasta
```

### **2. Processar em massa:**
```bash
python bulk_pdf_processor.py
# Aguardar processamento completo
```

### **3. Testar na interface:**
```bash
streamlit run multi_agent_app.py
# Fazer consulta na aba "Query Analysis"
```

### **4. Verificar citações:**
- Consulta deve retornar resposta com citações [1], [2], etc.
- Lista de referências deve aparecer no final
- Cada citação deve ter arquivo e página específicos

## 🎯 **Resultado Final**

Após o upload em massa, você terá:

✅ **Centenas de PDFs indexados** no sistema
✅ **Busca semântica** em todo o conteúdo
✅ **Citações automáticas** com referências precisas
✅ **Respostas baseadas em evidências** científicas
✅ **Rastreabilidade completa** das fontes
✅ **Sistema de qualidade** validando as referências

O sistema se torna uma **biblioteca médica inteligente** que pode consultar instantaneamente centenas de artigos científicos e fornecer respostas fundamentadas com citações precisas!

## 🚀 **Próximos Passos**

1. **Execute:** `python bulk_pdf_processor.py`
2. **Adicione seus PDFs** na pasta `pdfs_cientificos`
3. **Teste consultas** na interface web
4. **Verifique as citações** nas respostas
5. **Ajuste configurações** conforme necessário

Seu sistema multi-agente agora terá acesso a uma vasta biblioteca científica! 📚🤖
