"""
Sistema RAG Avançado - Versão com persistência e respostas detalhadas para análise de neurocovid
"""
import streamlit as st
import ollama
import logging
import os
import PyPDF2
import uuid
import time
import json
import pickle
import shutil
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuração da página
st.set_page_config(
    page_title="Sistema RAG - Análise Neurocovid",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS customizado
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-response {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        border-left: 4px solid #1f77b4;
    }
    .source-info {
        background-color: #e8f4f8;
        padding: 0.5rem;
        border-radius: 0.3rem;
        margin: 0.5rem 0;
        border-left: 3px solid #17a2b8;
        font-size: 0.9rem;
    }
</style>
""", unsafe_allow_html=True)

class PersistentDocumentStore:
    """Sistema avançado de armazenamento de documentos com persistência"""

    def __init__(self):
        self.documents = []
        self.ollama_client = None
        self.setup_directories()
        self.setup_ollama()
        self.load_existing_documents()

    def setup_directories(self):
        """Configura diretórios necessários"""
        self.base_dir = Path("./data")
        self.pdf_dir = self.base_dir / "pdfs"
        self.db_dir = self.base_dir / "database"
        self.metadata_file = self.db_dir / "documents_metadata.json"
        self.documents_file = self.db_dir / "documents_store.pkl"

        # Criar diretórios
        for dir_path in [self.base_dir, self.pdf_dir, self.db_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def setup_ollama(self):
        """Configura conexão com Ollama"""
        try:
            self.ollama_client = ollama.Client(host="http://localhost:11434")
            # Testar conexão
            models = self.ollama_client.list()
            logger.info("Ollama conectado com sucesso")
        except Exception as e:
            logger.error(f"Erro ao conectar com Ollama: {e}")
            self.ollama_client = None

    def load_existing_documents(self):
        """Carrega documentos existentes do armazenamento persistente"""
        try:
            if self.documents_file.exists():
                with open(self.documents_file, 'rb') as f:
                    self.documents = pickle.load(f)
                logger.info(f"Documentos existentes carregados")
            else:
                self.documents = []
                logger.info("Nenhum documento existente encontrado")
        except Exception as e:
            logger.error(f"Erro ao carregar documentos existentes: {e}")
            self.documents = []

    def save_documents(self):
        """Salva documentos no armazenamento persistente"""
        try:
            with open(self.documents_file, 'wb') as f:
                pickle.dump(self.documents, f)

            # Salvar metadados em JSON para facilitar visualização
            metadata = []
            for doc in self.documents:
                metadata.append({
                    "filename": doc["filename"],
                    "page": doc["page"],
                    "chunk_id": doc["chunk_id"],
                    "text_preview": doc["text"][:100] + "..." if len(doc["text"]) > 100 else doc["text"]
                })

            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

            logger.info(f"Documentos salvos com sucesso")
        except Exception as e:
            logger.error(f"Erro ao salvar documentos: {e}")

    def get_loaded_files(self) -> List[str]:
        """Retorna lista de arquivos já carregados"""
        loaded_files = set()
        for doc in self.documents:
            loaded_files.add(doc["filename"])
        return sorted(list(loaded_files))

    def save_uploaded_pdf(self, uploaded_file) -> str:
        """Salva PDF carregado no sistema de arquivos"""
        try:
            file_path = self.pdf_dir / uploaded_file.name
            with open(file_path, 'wb') as f:
                f.write(uploaded_file.getbuffer())
            logger.info(f"PDF salvo: {file_path}")
            return str(file_path)
        except Exception as e:
            logger.error(f"Erro ao salvar PDF: {e}")
            return None
    
    def extract_text_from_pdf(self, pdf_file) -> List[Dict]:
        """Extrai texto de um PDF"""
        chunks = []
        try:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            filename = pdf_file.name
            
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    text = page.extract_text()
                    if text.strip() and len(text.strip()) > 50:
                        # Dividir em chunks menores
                        chunk_size = 800
                        for i in range(0, len(text), chunk_size):
                            chunk_text = text[i:i + chunk_size].strip()
                            if len(chunk_text) > 50:
                                chunks.append({
                                    "text": chunk_text,
                                    "filename": filename,
                                    "page": page_num + 1,
                                    "chunk_id": str(uuid.uuid4())
                                })
                except Exception as e:
                    logger.warning(f"Erro ao processar página {page_num + 1}: {e}")
                    continue
            
            logger.info(f"Texto extraído de {filename}")
            return chunks
            
        except Exception as e:
            logger.error(f"Erro ao extrair texto do PDF: {e}")
            return []
    
    def add_documents(self, chunks: List[Dict]) -> bool:
        """Adiciona documentos ao armazenamento persistente"""
        try:
            # Verificar se já existem chunks deste arquivo
            existing_files = self.get_loaded_files()
            new_chunks = []

            for chunk in chunks:
                if chunk["filename"] not in existing_files:
                    new_chunks.append(chunk)

            if new_chunks:
                self.documents.extend(new_chunks)
                self.save_documents()  # Salvar automaticamente
                logger.info(f"Documentos adicionados com sucesso")
                return True
            else:
                logger.info("Documentos já existem no sistema")
                return True

        except Exception as e:
            logger.error(f"Erro ao adicionar documentos: {e}")
            return False
    
    def search_documents(self, query: str, max_results: int = 5) -> List[Dict]:
        """Busca avançada por palavras-chave com scoring melhorado"""
        try:
            query_lower = query.lower()
            query_words = [word.strip() for word in query_lower.split() if len(word.strip()) > 2]
            results = []

            # Palavras-chave médicas importantes para neurocovid
            medical_keywords = [
                'neurológic', 'covid', 'sars-cov-2', 'anosmia', 'ageusia', 'cefaleia',
                'encefal', 'meningite', 'avc', 'delirium', 'confusão', 'convulsão',
                'neuropatia', 'mielite', 'síndrome', 'manifestação', 'sintoma',
                'tratamento', 'diagnóstico', 'fisiopatologia', 'mecanismo'
            ]

            for doc in self.documents:
                text_lower = doc["text"].lower()
                score = 0

                # Pontuação por palavras da consulta
                for word in query_words:
                    if word in text_lower:
                        # Pontuação maior para palavras exatas
                        exact_matches = text_lower.count(word)
                        score += exact_matches * 3

                        # Pontuação para palavras parciais
                        for text_word in text_lower.split():
                            if word in text_word and word != text_word:
                                score += 1

                # Bonificação para palavras-chave médicas
                for keyword in medical_keywords:
                    if keyword in text_lower:
                        score += text_lower.count(keyword) * 2

                # Bonificação para documentos com múltiplas palavras da consulta
                words_found = sum(1 for word in query_words if word in text_lower)
                if words_found > 1:
                    score += words_found * 2

                if score > 0:
                    results.append({
                        "content": doc["text"],
                        "filename": doc["filename"],
                        "page": doc["page"],
                        "score": score
                    })

            # Ordenar por relevância
            results.sort(key=lambda x: x["score"], reverse=True)
            return results[:max_results]

        except Exception as e:
            logger.error(f"Erro na busca: {e}")
            return []

    def clean_reasoning(self, text: str) -> str:
        """Remove linhas de raciocínio interno do modelo"""
        import re

        # Padrões de raciocínio a remover
        reasoning_patterns = [
            r'<think>.*?</think>',  # Tags de pensamento
            r'<thinking>.*?</thinking>',
            r'Pensando sobre.*?\n',
            r'Analisando.*?\n',
            r'Vou analisar.*?\n',
            r'Primeiro.*?\n',
            r'Considerando.*?\n',
            r'Baseando-me.*?\n',
            r'Ao examinar.*?\n',
            r'Observando.*?\n'
        ]

        cleaned_text = text
        for pattern in reasoning_patterns:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.DOTALL | re.IGNORECASE)

        # Remover linhas que começam com indicadores de raciocínio
        lines = cleaned_text.split('\n')
        filtered_lines = []

        for line in lines:
            line_lower = line.strip().lower()
            # Pular linhas que indicam raciocínio
            if any(indicator in line_lower for indicator in [
                'pensando', 'analisando', 'considerando', 'examinando',
                'vou analisar', 'primeiro vou', 'baseando-me', 'observando'
            ]):
                continue
            filtered_lines.append(line)

        return '\n'.join(filtered_lines).strip()

    def clean_document_references(self, text: str, relevant_docs: List[Dict]) -> str:
        """Substitui referências genéricas por nomes reais dos arquivos"""
        import re

        # Criar mapeamento de documentos numerados para nomes reais
        doc_mapping = {}
        for i, doc in enumerate(relevant_docs):
            doc_mapping[f"DOCUMENTO {i+1}"] = doc['filename']
            doc_mapping[f"Documento {i+1}"] = doc['filename']
            doc_mapping[f"documento {i+1}"] = doc['filename']

        cleaned_text = text

        # Substituir referências do tipo [DOCUMENTO X - filename, página Y] por [filename, página Y]
        for doc_ref, filename in doc_mapping.items():
            # Padrão: [DOCUMENTO X - filename, página Y]
            pattern = rf'\[{re.escape(doc_ref)} - {re.escape(filename)}, página (\d+)\]'
            replacement = f'[{filename}, página \\1]'
            cleaned_text = re.sub(pattern, replacement, cleaned_text, flags=re.IGNORECASE)

            # Padrão: DOCUMENTO X - filename, página Y
            pattern = rf'{re.escape(doc_ref)} - {re.escape(filename)}, página (\d+)'
            replacement = f'{filename}, página \\1'
            cleaned_text = re.sub(pattern, replacement, cleaned_text, flags=re.IGNORECASE)

            # Padrão: conforme DOCUMENTO X
            pattern = rf'conforme {re.escape(doc_ref)}'
            replacement = f'conforme {filename}'
            cleaned_text = re.sub(pattern, replacement, cleaned_text, flags=re.IGNORECASE)

            # Padrão: segundo DOCUMENTO X
            pattern = rf'segundo {re.escape(doc_ref)}'
            replacement = f'segundo {filename}'
            cleaned_text = re.sub(pattern, replacement, cleaned_text, flags=re.IGNORECASE)

        return cleaned_text

    def generate_answer(self, query: str, relevant_docs: List[Dict]) -> str:
        """Gera resposta usando o modelo"""
        if not self.ollama_client:
            return "❌ Erro: Ollama não está disponível"
        
        try:
            # Construir contexto usando nomes reais dos arquivos
            context = ""
            sources = []

            for doc in relevant_docs:
                context += f"\n[{doc['filename']}, página {doc['page']}]:\n{doc['content']}\n"
                sources.append(f"{doc['filename']} (página {doc['page']})")
            
            if not context.strip():
                return "❌ Nenhum documento relevante encontrado. Carregue PDFs primeiro!"
            
            # Prompt otimizado para respostas detalhadas com citações diretas
            prompt = f"""
Baseado EXCLUSIVAMENTE nos documentos científicos fornecidos, forneça uma resposta DETALHADA e ABRANGENTE sobre neurocovid.

DOCUMENTOS CIENTÍFICOS:
{context}

PERGUNTA: {query}

INSTRUÇÕES PARA RESPOSTA DETALHADA:
- Forneça uma resposta COMPLETA e ABRANGENTE (mínimo 300-500 palavras)
- INCLUA todos os aspectos relevantes mencionados nos documentos
- EXPLIQUE conceitos médicos de forma detalhada
- DESCREVA mecanismos fisiopatológicos quando disponíveis
- LISTE sintomas, tratamentos, e manifestações de forma completa
- CORRELACIONE informações entre diferentes estudos/documentos
- USE terminologia médica precisa e profissional
- ORGANIZE a resposta em seções claras quando apropriado
- NÃO mostre processo de raciocínio, apenas a resposta final detalhada
- SEJA abrangente mas baseado apenas nos documentos fornecidos

INSTRUÇÕES ESPECÍFICAS PARA CITAÇÕES:
- CITE cada informação usando o formato: [NOME_DO_ARQUIVO.pdf, página X]
- USE o nome EXATO do arquivo PDF conforme mostrado nos documentos
- NÃO use referências genéricas como "Documento 1" ou "Documento 2"
- SEMPRE inclua o número da página junto com o nome do arquivo
- Quando múltiplas informações vêm do mesmo arquivo, repita o nome do arquivo
- Exemplo correto: [3156930-KLEBER FERNANDO PEREIRA.pdf, página 5]
- Exemplo correto: [Manifestacoes_neurologicas_da_Covid-19_uma_revisao.pdf, página 12]

ESTRUTURA SUGERIDA:
1. Definição/Conceito principal
2. Fisiopatologia (se disponível)
3. Manifestações clínicas detalhadas
4. Diagnóstico (se mencionado)
5. Tratamento/Manejo (se disponível)
6. Prognóstico/Complicações (se relevante)

RESPOSTA DETALHADA E ABRANGENTE:
"""

            response = self.ollama_client.chat(
                model="deepseek-r1:14b",
                messages=[
                    {
                        "role": "system",
                        "content": """Você é um especialista médico em manifestações neurológicas da COVID-19 (neurocovid).

CARACTERÍSTICAS DA SUA RESPOSTA:
- Forneça respostas DETALHADAS e ABRANGENTES (300-500+ palavras)
- Use linguagem médica precisa e profissional
- Explique conceitos complexos de forma completa
- Inclua todos os aspectos relevantes dos documentos
- Organize informações de forma estruturada
- Cite fontes específicas para cada informação
- NÃO mostre raciocínio interno, apenas a resposta final detalhada
- Baseie-se EXCLUSIVAMENTE nos documentos fornecidos"""
                    },
                    {"role": "user", "content": prompt}
                ],
                options={
                    "temperature": 0.2,  # Baixa para precisão médica
                    "top_p": 0.9,
                    "num_predict": 2048,  # Permitir respostas mais longas
                    "repeat_penalty": 1.1
                }
            )
            
            answer = response['message']['content']

            # Pós-processamento para remover raciocínio interno
            answer = self.clean_reasoning(answer)

            # Pós-processamento para limpar referências de documentos
            answer = self.clean_document_references(answer, relevant_docs)

            # Adicionar fontes
            sources_text = "\n\n📚 **Fontes consultadas:**\n"
            for source in set(sources):
                sources_text += f"• {source}\n"

            return answer + sources_text
            
        except Exception as e:
            logger.error(f"Erro ao gerar resposta: {e}")
            return f"❌ Erro ao processar: {str(e)}"

@st.cache_resource
def initialize_store():
    """Inicializa o sistema de documentos"""
    return PersistentDocumentStore()

def main():
    """Função principal"""
    
    # Header
    st.markdown('<h1 class="main-header">🧠 Sistema RAG Avançado - Análise Neurocovid</h1>', unsafe_allow_html=True)
    st.markdown("**Powered by deepseek-r1:14b | Respostas Detalhadas | Armazenamento Persistente**")
    st.markdown("---")
    
    # Inicializar sistema
    doc_store = initialize_store()
    
    # Verificar Ollama
    if not doc_store.ollama_client:
        st.error("❌ Ollama não está disponível. Verifique se está rodando.")
        st.stop()
    
    st.success("✅ Sistema conectado com deepseek-r1:14b")
    
    # Sidebar para upload
    with st.sidebar:
        st.header("📁 Upload de PDFs")

        uploaded_files = st.file_uploader(
            "Carregue seus PDFs sobre neurocovid:",
            type=["pdf"],
            accept_multiple_files=True,
            help="Selecione um ou múltiplos arquivos PDF"
        )
        
        if uploaded_files:
            if st.button("🚀 Processar PDFs"):
                progress_bar = st.progress(0)
                status_text = st.empty()

                try:
                    all_chunks = []
                    saved_files = []

                    for i, file in enumerate(uploaded_files):
                        status_text.text(f"Processando {file.name}...")

                        # Salvar PDF no sistema de arquivos
                        saved_path = doc_store.save_uploaded_pdf(file)
                        if saved_path:
                            saved_files.append(file.name)

                        # Extrair texto
                        chunks = doc_store.extract_text_from_pdf(file)
                        all_chunks.extend(chunks)
                        progress_bar.progress((i + 1) / len(uploaded_files))

                    if all_chunks:
                        status_text.text("Finalizando processamento...")
                        success = doc_store.add_documents(all_chunks)

                        if success:
                            st.success("✅ PDFs carregados com sucesso!")
                            st.balloons()
                        else:
                            st.error("❌ Erro ao processar documentos")
                    else:
                        st.error("❌ Não foi possível extrair texto dos PDFs")

                except Exception:
                    st.error("❌ Erro durante o processamento")

                finally:
                    progress_bar.empty()
                    status_text.empty()
    
    # Área principal
    st.header("💬 Chat com Base de Conhecimento")
    
    # Inicializar histórico
    if "robust_history" not in st.session_state:
        st.session_state.robust_history = []
    
    # Input
    user_query = st.text_area(
        "Sua pergunta sobre neurocovid:",
        placeholder="Ex: Quais são as principais manifestações neurológicas da COVID-19?",
        height=100
    )
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        if st.button("🔍 Consultar", type="primary"):
            if user_query.strip():
                if not doc_store.documents:
                    st.warning("⚠️ Carregue PDFs primeiro para fazer consultas!")
                else:
                    with st.spinner("🔍 Buscando informações..."):
                        relevant_docs = doc_store.search_documents(user_query, max_results=5)

                    if relevant_docs:
                        with st.spinner("🧠 Gerando resposta..."):
                            answer = doc_store.generate_answer(user_query, relevant_docs)

                        # Adicionar ao histórico
                        st.session_state.robust_history.append({
                            "query": user_query,
                            "answer": answer,
                            "sources": relevant_docs
                        })

                        st.rerun()
                    else:
                        st.warning("⚠️ Nenhuma informação relevante encontrada nos documentos!")
    
    with col2:
        if st.button("🗑️ Limpar"):
            st.session_state.robust_history = []
            st.rerun()
    
    # Exibir histórico
    if st.session_state.robust_history:
        st.markdown("### 💬 Histórico")
        
        for i, item in enumerate(reversed(st.session_state.robust_history)):
            with st.expander(f"Consulta {len(st.session_state.robust_history) - i}: {item['query'][:50]}..."):
                st.markdown(f"**❓ Pergunta:** {item['query']}")
                
                st.markdown("**🤖 Resposta:**")
                st.markdown(f"""
                <div class="agent-response">
                    {item['answer']}
                </div>
                """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
