"""
Research Synthesis Agent - Specialized in literature review and evidence synthesis
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
import ollama
import json
from datetime import datetime

from .base_agent import BaseAgent, Message, MessageType
from config.settings import OLLAMA_CONFIG

logger = logging.getLogger(__name__)


class ResearchSynthesisAgent(BaseAgent):
    """Agent specialized in research synthesis and evidence compilation"""
    
    def __init__(self):
        super().__init__(
            agent_id="research_synthesis",
            name="Research Synthesis Agent",
            role="Literature review and evidence synthesis specialist",
            capabilities=[
                "evidence_synthesis",
                "literature_review",
                "meta_analysis",
                "research_correlation",
                "report_generation"
            ]
        )
        
        self.ollama_client = None
        self.synthesis_templates = self._initialize_synthesis_templates()
        
        # Initialize Ollama client
        asyncio.create_task(self._initialize_ollama())
        
        # Register message handlers
        self.message_handlers[MessageType.REQUEST] = self._handle_request
    
    async def _initialize_ollama(self):
        """Initialize Ollama client"""
        try:
            self.ollama_client = ollama.Client(host=OLLAMA_CONFIG["base_url"])
            self.logger.info("Research Synthesis Agent initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing Ollama client: {e}")
    
    def _initialize_synthesis_templates(self) -> Dict[str, Any]:
        """Initialize synthesis templates"""
        return {
            "comprehensive_review": {
                "sections": [
                    "executive_summary",
                    "background",
                    "methodology",
                    "findings",
                    "clinical_implications",
                    "limitations",
                    "conclusions",
                    "recommendations"
                ],
                "min_sources": 3,
                "evidence_levels": ["high", "moderate", "low"]
            },
            "quick_summary": {
                "sections": [
                    "key_findings",
                    "clinical_relevance",
                    "recommendations"
                ],
                "min_sources": 1,
                "evidence_levels": ["any"]
            },
            "evidence_synthesis": {
                "sections": [
                    "evidence_overview",
                    "quality_assessment",
                    "synthesis_results",
                    "clinical_applications"
                ],
                "min_sources": 2,
                "evidence_levels": ["high", "moderate"]
            }
        }
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming synthesis request"""
        action = request.get("action", "")
        inputs = request.get("inputs", {})
        
        if action == "synthesize_evidence":
            return await self._synthesize_evidence(inputs)
        elif action == "quick_summary":
            return await self._quick_summary(inputs)
        elif action == "literature_review":
            return await self._literature_review(inputs)
        elif action == "correlate_findings":
            return await self._correlate_findings(inputs)
        else:
            return {"error": f"Unknown action: {action}"}
    
    async def _synthesize_evidence(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Synthesize evidence from multiple sources"""
        try:
            query = inputs.get("query", "")
            document_results = inputs.get("document_intelligence_result", {})
            diagnostic_results = inputs.get("diagnostic_analysis_result", {})
            treatment_results = inputs.get("treatment_recommendation_result", {})
            
            # Extract evidence from all sources
            evidence_sources = self._extract_evidence_sources(
                document_results, diagnostic_results, treatment_results
            )
            
            # Assess evidence quality
            evidence_quality = self._assess_evidence_quality(evidence_sources)
            
            # Generate synthesis
            synthesis_result = await self._generate_evidence_synthesis(
                query, evidence_sources, evidence_quality
            )
            
            # Create structured report
            structured_report = await self._create_structured_report(
                query, evidence_sources, synthesis_result, "evidence_synthesis"
            )
            
            return {
                "query": query,
                "evidence_sources": evidence_sources,
                "evidence_quality": evidence_quality,
                "synthesized_response": synthesis_result,
                "structured_report": structured_report,
                "synthesis_confidence": self._calculate_synthesis_confidence(evidence_sources)
            }
            
        except Exception as e:
            self.logger.error(f"Error synthesizing evidence: {e}")
            return {"error": str(e)}
    
    async def _quick_summary(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Generate quick summary of findings"""
        try:
            query = inputs.get("query", "")
            document_results = inputs.get("document_intelligence_result", {})
            
            # Extract key information
            key_documents = document_results.get("documents", [])[:3]
            
            if not key_documents:
                return {"error": "No documents available for summary"}
            
            # Generate quick summary
            summary = await self._generate_quick_summary(query, key_documents)
            
            # Extract key points
            key_points = self._extract_key_points(key_documents)
            
            return {
                "query": query,
                "quick_summary": summary,
                "key_points": key_points,
                "sources_used": len(key_documents),
                "summary_type": "quick"
            }
            
        except Exception as e:
            self.logger.error(f"Error generating quick summary: {e}")
            return {"error": str(e)}
    
    async def _literature_review(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Conduct comprehensive literature review"""
        try:
            query = inputs.get("query", "")
            document_results = inputs.get("document_intelligence_result", {})
            
            documents = document_results.get("documents", [])
            
            if len(documents) < 2:
                return {"error": "Insufficient documents for literature review"}
            
            # Categorize documents
            categorized_docs = self._categorize_documents(documents)
            
            # Generate literature review
            literature_review = await self._generate_literature_review(
                query, categorized_docs
            )
            
            # Create comprehensive report
            comprehensive_report = await self._create_structured_report(
                query, documents, literature_review, "comprehensive_review"
            )
            
            return {
                "query": query,
                "literature_review": literature_review,
                "document_categories": categorized_docs,
                "comprehensive_report": comprehensive_report,
                "total_sources": len(documents)
            }
            
        except Exception as e:
            self.logger.error(f"Error conducting literature review: {e}")
            return {"error": str(e)}
    
    async def _correlate_findings(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Correlate findings across different studies"""
        try:
            documents = inputs.get("documents", [])
            
            if len(documents) < 2:
                return {"error": "Need at least 2 documents for correlation"}
            
            # Extract findings from each document
            document_findings = []
            for doc in documents:
                findings = await self._extract_document_findings(doc)
                document_findings.append(findings)
            
            # Find correlations
            correlations = self._find_correlations(document_findings)
            
            # Generate correlation analysis
            correlation_analysis = await self._generate_correlation_analysis(
                document_findings, correlations
            )
            
            return {
                "document_findings": document_findings,
                "correlations": correlations,
                "correlation_analysis": correlation_analysis,
                "correlation_strength": self._assess_correlation_strength(correlations)
            }
            
        except Exception as e:
            self.logger.error(f"Error correlating findings: {e}")
            return {"error": str(e)}
    
    def _extract_evidence_sources(self, document_results: Dict, 
                                 diagnostic_results: Dict, 
                                 treatment_results: Dict) -> List[Dict]:
        """Extract evidence from all agent results"""
        sources = []
        
        # Document intelligence sources
        if document_results and "documents" in document_results:
            for doc in document_results["documents"]:
                sources.append({
                    "type": "literature",
                    "source": doc.get("filename", "unknown"),
                    "content": doc.get("content", ""),
                    "relevance_score": doc.get("relevance_score", 0),
                    "page": doc.get("page", 0)
                })
        
        # Diagnostic analysis insights
        if diagnostic_results and "diagnostic_insights" in diagnostic_results:
            sources.append({
                "type": "diagnostic_analysis",
                "source": "diagnostic_agent",
                "content": diagnostic_results["diagnostic_insights"],
                "relevance_score": 0.9
            })
        
        # Treatment recommendations
        if treatment_results and "comprehensive_plan" in treatment_results:
            sources.append({
                "type": "treatment_recommendations",
                "source": "treatment_agent",
                "content": treatment_results["comprehensive_plan"],
                "relevance_score": 0.8
            })
        
        return sources
    
    def _assess_evidence_quality(self, sources: List[Dict]) -> Dict[str, Any]:
        """Assess overall evidence quality"""
        quality_scores = []
        source_types = {"literature": 0, "diagnostic_analysis": 0, "treatment_recommendations": 0}
        
        for source in sources:
            # Base quality on source type and relevance
            base_quality = 0.7 if source["type"] == "literature" else 0.8
            relevance_bonus = source.get("relevance_score", 0) * 0.3
            quality_score = min(base_quality + relevance_bonus, 1.0)
            quality_scores.append(quality_score)
            
            source_types[source["type"]] += 1
        
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        
        # Determine overall quality level
        if avg_quality >= 0.8 and len(sources) >= 3:
            overall_quality = "high"
        elif avg_quality >= 0.6 and len(sources) >= 2:
            overall_quality = "moderate"
        else:
            overall_quality = "low"
        
        return {
            "overall_quality": overall_quality,
            "average_score": avg_quality,
            "source_distribution": source_types,
            "total_sources": len(sources)
        }
    
    async def _generate_evidence_synthesis(self, query: str, sources: List[Dict],
                                         quality: Dict) -> str:
        """Generate evidence synthesis using AI with proper citations"""
        try:
            # Prepare sources for synthesis with citation information
            sources_text = ""
            citations_list = []

            for i, source in enumerate(sources[:10]):  # Increase to top 10 sources
                citation_key = f"[{i+1}]"
                filename = source.get('source', 'Unknown')
                page = source.get('page', 'N/A') if 'page' in source else 'N/A'

                # Create citation entry
                citations_list.append({
                    "key": citation_key,
                    "filename": filename,
                    "page": page,
                    "content_preview": source['content'][:200] + "..." if len(source['content']) > 200 else source['content']
                })

                sources_text += f"\n{citation_key} {filename} (Page {page}): {source['content'][:500]}...\n"

            # Create citations reference list
            citations_text = "\n\nREFERENCES:\n"
            for citation in citations_list:
                citations_text += f"{citation['key']} {citation['filename']}, Page {citation['page']}\n"

            prompt = f"""
            As a medical researcher specializing in neurocovid, synthesize the following evidence to answer: "{query}"

            Evidence Sources:
            {sources_text}

            Evidence Quality Assessment: {json.dumps(quality, indent=2)}

            Please provide a comprehensive evidence synthesis that:
            1. Integrates findings from all sources with proper citations [1], [2], etc.
            2. Identifies consistent patterns and themes across studies
            3. Notes any contradictions or limitations with source citations
            4. Provides clinical implications supported by evidence
            5. Suggests areas for further research
            6. Includes appropriate caveats about evidence quality
            7. Uses in-text citations throughout (e.g., "Studies show that anosmia is common [1,3,5]")

            IMPORTANT:
            - Cite sources using the format [1], [2], [3] etc. throughout your response
            - Include multiple citations when multiple sources support a point
            - Be specific about which sources support which claims
            - Include the reference list at the end

            Structure your response as a formal medical synthesis with proper academic citations.
            {citations_text}
            """

            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.2, "num_predict": 2048}
            )

            synthesis_content = response["message"]["content"]

            # Ensure citations are included
            if not any(f"[{i+1}]" in synthesis_content for i in range(len(citations_list))):
                # Add citations if AI didn't include them
                synthesis_content += "\n\n" + citations_text

            return synthesis_content

        except Exception as e:
            self.logger.error(f"Error generating evidence synthesis: {e}")
            return "Error generating evidence synthesis"
    
    async def _generate_quick_summary(self, query: str, documents: List[Dict]) -> str:
        """Generate quick summary"""
        try:
            docs_text = "\n\n".join([
                f"[{doc.get('filename', 'unknown')}]: {doc.get('content', '')[:300]}..."
                for doc in documents
            ])
            
            prompt = f"""
            Provide a concise summary for the query: "{query}"
            
            Based on these documents:
            {docs_text}
            
            Provide a brief, focused summary highlighting:
            1. Key findings relevant to the query
            2. Main clinical implications
            3. Important recommendations
            
            Keep the summary under 200 words.
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.2, "num_predict": 256}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating quick summary: {e}")
            return "Error generating quick summary"
    
    def _extract_key_points(self, documents: List[Dict]) -> List[str]:
        """Extract key points from documents"""
        key_points = []
        
        # Simple keyword-based extraction
        important_keywords = [
            "manifestation", "symptom", "treatment", "diagnosis", "prognosis",
            "mechanism", "pathophysiology", "therapy", "intervention", "outcome"
        ]
        
        for doc in documents:
            content = doc.get("content", "").lower()
            sentences = content.split(".")
            
            for sentence in sentences:
                if any(keyword in sentence for keyword in important_keywords):
                    if len(sentence.strip()) > 20 and len(sentence.strip()) < 200:
                        key_points.append(sentence.strip().capitalize())
                        if len(key_points) >= 5:  # Limit to 5 key points
                            break
            
            if len(key_points) >= 5:
                break
        
        return key_points
    
    def _categorize_documents(self, documents: List[Dict]) -> Dict[str, List[Dict]]:
        """Categorize documents by type/content"""
        categories = {
            "clinical_studies": [],
            "case_reports": [],
            "reviews": [],
            "guidelines": [],
            "other": []
        }
        
        for doc in documents:
            content = doc.get("content", "").lower()
            filename = doc.get("filename", "").lower()
            
            if any(term in content or term in filename for term in ["study", "trial", "cohort"]):
                categories["clinical_studies"].append(doc)
            elif any(term in content or term in filename for term in ["case", "report"]):
                categories["case_reports"].append(doc)
            elif any(term in content or term in filename for term in ["review", "meta-analysis"]):
                categories["reviews"].append(doc)
            elif any(term in content or term in filename for term in ["guideline", "protocol"]):
                categories["guidelines"].append(doc)
            else:
                categories["other"].append(doc)
        
        return categories
    
    async def _generate_literature_review(self, query: str, categorized_docs: Dict) -> str:
        """Generate comprehensive literature review"""
        try:
            # Prepare categorized content
            review_sections = []
            
            for category, docs in categorized_docs.items():
                if docs:
                    docs_summary = "\n".join([
                        f"- {doc.get('filename', 'unknown')}: {doc.get('content', '')[:200]}..."
                        for doc in docs[:3]  # Limit to 3 docs per category
                    ])
                    review_sections.append(f"{category.replace('_', ' ').title()}:\n{docs_summary}")
            
            sections_text = "\n\n".join(review_sections)
            
            prompt = f"""
            Conduct a comprehensive literature review for: "{query}"
            
            Available Literature by Category:
            {sections_text}
            
            Provide a structured literature review with:
            1. Introduction and background
            2. Methodology (describe the available evidence)
            3. Results by study type
            4. Discussion of findings
            5. Clinical implications
            6. Limitations and gaps
            7. Conclusions and future directions
            
            Maintain academic rigor and cite sources appropriately.
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.2, "num_predict": 2048}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating literature review: {e}")
            return "Error generating literature review"
    
    async def _create_structured_report(self, query: str, sources: List[Dict], 
                                      content: str, template_type: str) -> Dict[str, Any]:
        """Create structured report based on template"""
        template = self.synthesis_templates.get(template_type, {})
        
        report = {
            "title": f"Research Synthesis Report: {query}",
            "generated_date": datetime.now().isoformat(),
            "query": query,
            "template_type": template_type,
            "sources_count": len(sources),
            "content": content,
            "sections": template.get("sections", []),
            "evidence_quality": "moderate",  # Default
            "recommendations": self._extract_recommendations(content)
        }
        
        return report
    
    def _extract_recommendations(self, content: str) -> List[str]:
        """Extract recommendations from content"""
        recommendations = []
        
        # Simple extraction based on keywords
        sentences = content.split(".")
        for sentence in sentences:
            if any(word in sentence.lower() for word in ["recommend", "suggest", "should", "consider"]):
                if len(sentence.strip()) > 20:
                    recommendations.append(sentence.strip())
                    if len(recommendations) >= 5:
                        break
        
        return recommendations
    
    def _calculate_synthesis_confidence(self, sources: List[Dict]) -> str:
        """Calculate confidence in synthesis"""
        if len(sources) >= 3 and any(s["type"] == "literature" for s in sources):
            return "high"
        elif len(sources) >= 2:
            return "moderate"
        else:
            return "low"
    
    async def _extract_document_findings(self, document: Dict) -> Dict[str, Any]:
        """Extract findings from a single document"""
        # Simplified extraction - in production, this could use NLP
        content = document.get("content", "")
        
        return {
            "source": document.get("filename", "unknown"),
            "key_findings": content[:200] + "...",
            "methodology": "Not extracted",
            "sample_size": "Not specified",
            "conclusions": "Not extracted"
        }
    
    def _find_correlations(self, findings_list: List[Dict]) -> List[Dict]:
        """Find correlations between findings"""
        correlations = []
        
        # Simple correlation detection based on common keywords
        for i, findings1 in enumerate(findings_list):
            for j, findings2 in enumerate(findings_list[i+1:], i+1):
                # Check for common themes
                content1 = findings1.get("key_findings", "").lower()
                content2 = findings2.get("key_findings", "").lower()
                
                common_words = set(content1.split()) & set(content2.split())
                medical_words = [w for w in common_words if len(w) > 5]
                
                if len(medical_words) >= 2:
                    correlations.append({
                        "source1": findings1.get("source", "unknown"),
                        "source2": findings2.get("source", "unknown"),
                        "common_themes": list(medical_words)[:3],
                        "correlation_strength": "moderate"
                    })
        
        return correlations
    
    def _assess_correlation_strength(self, correlations: List[Dict]) -> str:
        """Assess overall correlation strength"""
        if len(correlations) >= 3:
            return "strong"
        elif len(correlations) >= 1:
            return "moderate"
        else:
            return "weak"
    
    async def _generate_correlation_analysis(self, findings: List[Dict], 
                                           correlations: List[Dict]) -> str:
        """Generate correlation analysis"""
        try:
            prompt = f"""
            Analyze correlations between research findings:
            
            Findings: {json.dumps(findings, indent=2)}
            Correlations: {json.dumps(correlations, indent=2)}
            
            Provide an analysis of:
            1. Consistent patterns across studies
            2. Contradictory findings
            3. Strength of evidence
            4. Clinical implications of correlations
            5. Limitations of the correlation analysis
            """
            
            response = self.ollama_client.chat(
                model=OLLAMA_CONFIG["model"],
                messages=[{"role": "user", "content": prompt}],
                options={"temperature": 0.2, "num_predict": 1024}
            )
            
            return response["message"]["content"]
            
        except Exception as e:
            self.logger.error(f"Error generating correlation analysis: {e}")
            return "Error generating correlation analysis"
    
    async def _handle_request(self, message: Message):
        """Handle incoming request"""
        try:
            result = await self.process_request(message.content)
            
            response = Message(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.RESPONSE,
                content=result,
                correlation_id=message.correlation_id
            )
            
            await self.send_message(response)
            
        except Exception as e:
            self.logger.error(f"Error handling request: {e}")
