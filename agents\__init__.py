"""
Multi-Agent Medical System - Agents Module
"""

from .base_agent import BaseAgent, Message, MessageType, AgentStatus
from .message_bus import MessageB<PERSON>, message_bus
from .coordinator_agent import CoordinatorAgent
from .document_intelligence_agent import DocumentIntelligenceAgent
from .diagnostic_agent import DiagnosticAnalysisAgent
from .treatment_agent import TreatmentRecommendationAgent
from .research_synthesis_agent import ResearchSynthesisAgent
from .quality_assurance_agent import QualityAssuranceAgent
from .agent_manager import AgentManager, agent_manager

__all__ = [
    'BaseAgent',
    'Message', 
    'MessageType',
    'AgentStatus',
    'MessageBus',
    'message_bus',
    'CoordinatorAgent',
    'DocumentIntelligenceAgent',
    'DiagnosticAnalysisAgent',
    'TreatmentRecommendationAgent',
    'ResearchSynthesisAgent',
    'QualityAssuranceAgent',
    'AgentManager',
    'agent_manager'
]
