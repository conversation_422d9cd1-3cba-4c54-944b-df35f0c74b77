"""
Base Agent Class for Multi-Agent Medical System
"""
import asyncio
import logging
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Types of messages that can be sent between agents"""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"
    HEARTBEAT = "heartbeat"


class AgentStatus(Enum):
    """Agent status states"""
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    OFFLINE = "offline"


@dataclass
class Message:
    """Message structure for inter-agent communication"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    receiver_id: str = ""
    message_type: MessageType = MessageType.REQUEST
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    correlation_id: Optional[str] = None
    priority: int = 1  # 1=low, 5=high
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for serialization"""
        return {
            "id": self.id,
            "sender_id": self.sender_id,
            "receiver_id": self.receiver_id,
            "message_type": self.message_type.value,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "correlation_id": self.correlation_id,
            "priority": self.priority
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary"""
        return cls(
            id=data["id"],
            sender_id=data["sender_id"],
            receiver_id=data["receiver_id"],
            message_type=MessageType(data["message_type"]),
            content=data["content"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            correlation_id=data.get("correlation_id"),
            priority=data.get("priority", 1)
        )


class BaseAgent(ABC):
    """Base class for all agents in the multi-agent system"""
    
    def __init__(self, agent_id: str, name: str, role: str, capabilities: List[str] = None):
        self.agent_id = agent_id
        self.name = name
        self.role = role
        self.capabilities = capabilities or []
        self.status = AgentStatus.IDLE
        self.message_queue = asyncio.Queue()
        self.message_handlers: Dict[MessageType, Callable] = {}
        self.running = False
        self.logger = logging.getLogger(f"{__name__}.{self.agent_id}")
        
        # Performance metrics
        self.messages_processed = 0
        self.errors_count = 0
        self.start_time = None
        
        # Register default message handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default message handlers"""
        self.message_handlers[MessageType.HEARTBEAT] = self._handle_heartbeat
        self.message_handlers[MessageType.ERROR] = self._handle_error
    
    async def start(self):
        """Start the agent"""
        self.running = True
        self.start_time = datetime.now()
        self.status = AgentStatus.IDLE
        self.logger.info(f"Agent {self.name} ({self.agent_id}) started")
        
        # Start message processing loop
        asyncio.create_task(self._message_processing_loop())
    
    async def stop(self):
        """Stop the agent"""
        self.running = False
        self.status = AgentStatus.OFFLINE
        self.logger.info(f"Agent {self.name} ({self.agent_id}) stopped")
    
    async def _message_processing_loop(self):
        """Main message processing loop"""
        while self.running:
            try:
                # Wait for message with timeout
                message = await asyncio.wait_for(
                    self.message_queue.get(), 
                    timeout=1.0
                )
                
                await self._process_message(message)
                self.messages_processed += 1
                
            except asyncio.TimeoutError:
                # No message received, continue
                continue
            except Exception as e:
                self.errors_count += 1
                self.logger.error(f"Error processing message: {e}")
                self.status = AgentStatus.ERROR
    
    async def _process_message(self, message: Message):
        """Process incoming message"""
        self.status = AgentStatus.BUSY
        
        try:
            handler = self.message_handlers.get(message.message_type)
            if handler:
                await handler(message)
            else:
                self.logger.warning(f"No handler for message type: {message.message_type}")
        
        except Exception as e:
            self.logger.error(f"Error handling message {message.id}: {e}")
            # Send error response if this was a request
            if message.message_type == MessageType.REQUEST:
                error_response = Message(
                    sender_id=self.agent_id,
                    receiver_id=message.sender_id,
                    message_type=MessageType.ERROR,
                    content={"error": str(e), "original_message_id": message.id},
                    correlation_id=message.correlation_id
                )
                await self.send_message(error_response)
        
        finally:
            self.status = AgentStatus.IDLE
    
    async def send_message(self, message: Message):
        """Send message to another agent (to be implemented by message bus)"""
        # This will be implemented by the message bus
        pass
    
    async def receive_message(self, message: Message):
        """Receive message from message bus"""
        await self.message_queue.put(message)
    
    async def _handle_heartbeat(self, message: Message):
        """Handle heartbeat message"""
        response = Message(
            sender_id=self.agent_id,
            receiver_id=message.sender_id,
            message_type=MessageType.RESPONSE,
            content={
                "status": self.status.value,
                "messages_processed": self.messages_processed,
                "errors_count": self.errors_count,
                "uptime": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
            },
            correlation_id=message.correlation_id
        )
        await self.send_message(response)
    
    async def _handle_error(self, message: Message):
        """Handle error message"""
        self.logger.error(f"Received error from {message.sender_id}: {message.content}")
    
    @abstractmethod
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process a specific request (to be implemented by subclasses)"""
        pass
    
    def get_info(self) -> Dict[str, Any]:
        """Get agent information"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "role": self.role,
            "capabilities": self.capabilities,
            "status": self.status.value,
            "messages_processed": self.messages_processed,
            "errors_count": self.errors_count,
            "uptime": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        }
