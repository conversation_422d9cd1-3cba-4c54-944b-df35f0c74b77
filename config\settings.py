"""
Configurações do sistema de multiagentes para análise de neurocovid
"""
import os
from pathlib import Path
from typing import Dict, Any

# Diretórios base
BASE_DIR = Path(__file__).parent.parent
DATA_DIR = BASE_DIR / "data"
PDF_DIR = DATA_DIR / "pdfs"
DB_DIR = DATA_DIR / "database"

# Criar diretórios se não existirem
for dir_path in [DATA_DIR, PDF_DIR, DB_DIR]:
    dir_path.mkdir(parents=True, exist_ok=True)

# Configurações do Ollama
OLLAMA_CONFIG = {
    "base_url": "http://localhost:11434",
    "model": "llama2:7b",  # <PERSON>o padrão
    "embedding_model": "nomic-embed-text",
    "timeout": 120
}

# Configurações do banco de dados vetorial
VECTOR_DB_CONFIG = {
    "collection_name": "neurocovid_articles",
    "persist_directory": str(DB_DIR),
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "embedding_dimension": 768
}

# Configurações dos agentes
AGENTS_CONFIG = {
    "search_agent": {
        "name": "Agente de Busca",
        "role": "Especialista em busca e indexação de documentos médicos",
        "max_tokens": 2048,
        "temperature": 0.1
    },
    "analysis_agent": {
        "name": "Agente de Análise Médica",
        "role": "Especialista em análise de conteúdo médico sobre neurocovid",
        "max_tokens": 4096,
        "temperature": 0.2
    },
    "synthesis_agent": {
        "name": "Agente de Síntese",
        "role": "Especialista em síntese e geração de relatórios médicos",
        "max_tokens": 3072,
        "temperature": 0.3
    },
    "coordinator_agent": {
        "name": "Agente Coordenador",
        "role": "Coordena as atividades entre os agentes especializados",
        "max_tokens": 2048,
        "temperature": 0.1
    }
}

# Configurações do Streamlit
STREAMLIT_CONFIG = {
    "page_title": "Sistema de Análise Neurocovid",
    "page_icon": "🧠",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# Configurações de processamento de PDF
PDF_CONFIG = {
    "max_file_size": 50 * 1024 * 1024,  # 50MB
    "allowed_extensions": [".pdf"],
    "extract_images": True,
    "extract_tables": True
}

# Prompts do sistema
SYSTEM_PROMPTS = {
    "search_agent": """
    Você é um especialista em busca e indexação de documentos médicos sobre neurocovid.
    Sua função é:
    1. Analisar consultas de usuários
    2. Buscar documentos relevantes no banco de dados
    3. Extrair informações pertinentes
    4. Fornecer contexto médico apropriado
    """,
    
    "analysis_agent": """
    Você é um especialista em análise de conteúdo médico sobre neurocovid.
    Sua função é:
    1. Analisar artigos científicos sobre manifestações neurológicas da COVID-19
    2. Identificar padrões, sintomas e tratamentos
    3. Correlacionar informações entre diferentes estudos
    4. Fornecer insights clínicos baseados em evidências
    """,
    
    "synthesis_agent": """
    Você é um especialista em síntese e geração de relatórios médicos.
    Sua função é:
    1. Compilar informações de múltiplas fontes
    2. Gerar resumos executivos
    3. Criar relatórios estruturados
    4. Apresentar conclusões de forma clara e objetiva
    """,
    
    "coordinator_agent": """
    Você é o coordenador do sistema de multiagentes.
    Sua função é:
    1. Receber consultas dos usuários
    2. Determinar quais agentes devem ser acionados
    3. Coordenar o fluxo de trabalho entre agentes
    4. Compilar respostas finais para o usuário
    """
}

# Configurações de logging
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": str(BASE_DIR / "logs" / "system.log")
}
