"""
Medical Data Security and Compliance Module
Implements HIPAA compliance and medical data protection
"""
import hashlib
import logging
import secrets
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import re

logger = logging.getLogger(__name__)


class MedicalDataClassifier:
    """Classifies and identifies medical data types for appropriate handling"""
    
    def __init__(self):
        self.phi_patterns = self._initialize_phi_patterns()
        self.medical_terms = self._initialize_medical_terms()
    
    def _initialize_phi_patterns(self) -> Dict[str, str]:
        """Initialize PHI (Protected Health Information) patterns"""
        return {
            "ssn": r"\b\d{3}-\d{2}-\d{4}\b",
            "phone": r"\b\d{3}-\d{3}-\d{4}\b",
            "email": r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
            "mrn": r"\b(MRN|Medical Record Number)[\s:]*\d+\b",
            "date_of_birth": r"\b(DOB|Date of Birth)[\s:]*\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b",
            "address": r"\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln)\b"
        }
    
    def _initialize_medical_terms(self) -> List[str]:
        """Initialize medical terminology for classification"""
        return [
            "patient", "diagnosis", "treatment", "medication", "symptom",
            "procedure", "surgery", "therapy", "condition", "disease",
            "medical history", "clinical", "healthcare", "hospital",
            "physician", "doctor", "nurse", "prescription", "dosage"
        ]
    
    def classify_content(self, content: str) -> Dict[str, Any]:
        """Classify content for medical data sensitivity"""
        classification = {
            "contains_phi": False,
            "phi_types": [],
            "medical_relevance": 0.0,
            "sensitivity_level": "low",
            "requires_encryption": False,
            "retention_period": "standard"
        }
        
        # Check for PHI patterns
        for phi_type, pattern in self.phi_patterns.items():
            if re.search(pattern, content, re.IGNORECASE):
                classification["contains_phi"] = True
                classification["phi_types"].append(phi_type)
        
        # Calculate medical relevance
        content_lower = content.lower()
        medical_term_count = sum(1 for term in self.medical_terms if term in content_lower)
        classification["medical_relevance"] = min(medical_term_count / 10, 1.0)
        
        # Determine sensitivity level
        if classification["contains_phi"]:
            classification["sensitivity_level"] = "high"
            classification["requires_encryption"] = True
            classification["retention_period"] = "extended"
        elif classification["medical_relevance"] > 0.5:
            classification["sensitivity_level"] = "medium"
            classification["requires_encryption"] = True
        
        return classification


class EncryptionManager:
    """Manages encryption and decryption of medical data"""
    
    def __init__(self, master_key: Optional[bytes] = None):
        self.master_key = master_key or self._generate_master_key()
        self.fernet = Fernet(self.master_key)
    
    def _generate_master_key(self) -> bytes:
        """Generate a master encryption key"""
        return Fernet.generate_key()
    
    def encrypt_data(self, data: Union[str, Dict]) -> Dict[str, Any]:
        """Encrypt sensitive data"""
        try:
            # Convert to string if necessary
            if isinstance(data, dict):
                data_str = json.dumps(data)
            else:
                data_str = str(data)
            
            # Encrypt
            encrypted_data = self.fernet.encrypt(data_str.encode())
            
            return {
                "encrypted_data": base64.b64encode(encrypted_data).decode(),
                "encryption_timestamp": datetime.now().isoformat(),
                "encryption_method": "fernet",
                "is_encrypted": True
            }
            
        except Exception as e:
            logger.error(f"Encryption error: {e}")
            raise
    
    def decrypt_data(self, encrypted_package: Dict[str, Any]) -> Union[str, Dict]:
        """Decrypt sensitive data"""
        try:
            if not encrypted_package.get("is_encrypted", False):
                return encrypted_package
            
            # Decode and decrypt
            encrypted_data = base64.b64decode(encrypted_package["encrypted_data"])
            decrypted_bytes = self.fernet.decrypt(encrypted_data)
            decrypted_str = decrypted_bytes.decode()
            
            # Try to parse as JSON
            try:
                return json.loads(decrypted_str)
            except json.JSONDecodeError:
                return decrypted_str
                
        except Exception as e:
            logger.error(f"Decryption error: {e}")
            raise
    
    def hash_identifier(self, identifier: str) -> str:
        """Create a hash of an identifier for de-identification"""
        return hashlib.sha256(identifier.encode()).hexdigest()[:16]


class AccessControlManager:
    """Manages access control for medical data"""
    
    def __init__(self):
        self.access_policies = self._initialize_access_policies()
        self.user_sessions = {}
    
    def _initialize_access_policies(self) -> Dict[str, Any]:
        """Initialize access control policies"""
        return {
            "roles": {
                "physician": {
                    "permissions": ["read_all", "write_diagnosis", "write_treatment"],
                    "data_access": ["phi", "medical_records", "treatment_plans"]
                },
                "nurse": {
                    "permissions": ["read_limited", "write_observations"],
                    "data_access": ["medical_records", "treatment_plans"]
                },
                "researcher": {
                    "permissions": ["read_anonymized"],
                    "data_access": ["anonymized_data", "aggregated_data"]
                },
                "system": {
                    "permissions": ["read_all", "write_all", "admin"],
                    "data_access": ["all"]
                }
            },
            "data_classifications": {
                "phi": {"min_role": "physician", "encryption_required": True},
                "medical_records": {"min_role": "nurse", "encryption_required": True},
                "treatment_plans": {"min_role": "nurse", "encryption_required": False},
                "anonymized_data": {"min_role": "researcher", "encryption_required": False}
            }
        }
    
    def authenticate_user(self, user_id: str, role: str, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """Authenticate user and create session"""
        # Simplified authentication - in production, use proper auth
        session_token = secrets.token_urlsafe(32)
        session_expiry = datetime.now() + timedelta(hours=8)
        
        session = {
            "user_id": user_id,
            "role": role,
            "session_token": session_token,
            "created_at": datetime.now().isoformat(),
            "expires_at": session_expiry.isoformat(),
            "permissions": self.access_policies["roles"].get(role, {}).get("permissions", []),
            "data_access": self.access_policies["roles"].get(role, {}).get("data_access", [])
        }
        
        self.user_sessions[session_token] = session
        
        return {
            "authenticated": True,
            "session_token": session_token,
            "expires_at": session_expiry.isoformat(),
            "permissions": session["permissions"]
        }
    
    def validate_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """Validate user session"""
        session = self.user_sessions.get(session_token)
        
        if not session:
            return None
        
        # Check expiry
        expiry = datetime.fromisoformat(session["expires_at"])
        if datetime.now() > expiry:
            del self.user_sessions[session_token]
            return None
        
        return session
    
    def check_access_permission(self, session_token: str, data_type: str, 
                               operation: str) -> Dict[str, Any]:
        """Check if user has permission for specific operation"""
        session = self.validate_session(session_token)
        
        if not session:
            return {"authorized": False, "reason": "Invalid or expired session"}
        
        # Check data access
        user_data_access = session.get("data_access", [])
        if data_type not in user_data_access and "all" not in user_data_access:
            return {"authorized": False, "reason": f"No access to {data_type}"}
        
        # Check operation permission
        user_permissions = session.get("permissions", [])
        required_permission = self._get_required_permission(operation)
        
        if required_permission not in user_permissions and "admin" not in user_permissions:
            return {"authorized": False, "reason": f"Insufficient permissions for {operation}"}
        
        return {"authorized": True, "user_id": session["user_id"], "role": session["role"]}
    
    def _get_required_permission(self, operation: str) -> str:
        """Map operation to required permission"""
        operation_permissions = {
            "read": "read_limited",
            "read_all": "read_all",
            "write": "write_diagnosis",
            "delete": "admin",
            "export": "read_all"
        }
        return operation_permissions.get(operation, "admin")


class AuditLogger:
    """Logs all access and operations for compliance"""
    
    def __init__(self):
        self.audit_log = []
    
    def log_access(self, user_id: str, operation: str, data_type: str, 
                   resource_id: str, success: bool, details: Dict[str, Any] = None):
        """Log data access event"""
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "operation": operation,
            "data_type": data_type,
            "resource_id": resource_id,
            "success": success,
            "details": details or {},
            "ip_address": "127.0.0.1",  # In production, get real IP
            "user_agent": "multi-agent-system"
        }
        
        self.audit_log.append(audit_entry)
        logger.info(f"Audit: {user_id} {operation} {data_type} - {'SUCCESS' if success else 'FAILED'}")
    
    def log_data_modification(self, user_id: str, data_type: str, resource_id: str,
                             operation: str, old_value: Any = None, new_value: Any = None):
        """Log data modification event"""
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "operation": f"modify_{operation}",
            "data_type": data_type,
            "resource_id": resource_id,
            "old_value_hash": hashlib.sha256(str(old_value).encode()).hexdigest()[:16] if old_value else None,
            "new_value_hash": hashlib.sha256(str(new_value).encode()).hexdigest()[:16] if new_value else None,
            "modification_type": operation
        }
        
        self.audit_log.append(audit_entry)
        logger.info(f"Data modification: {user_id} modified {data_type} {resource_id}")
    
    def get_audit_trail(self, user_id: Optional[str] = None, 
                       start_date: Optional[datetime] = None,
                       end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get audit trail with optional filters"""
        filtered_log = self.audit_log
        
        if user_id:
            filtered_log = [entry for entry in filtered_log if entry["user_id"] == user_id]
        
        if start_date:
            filtered_log = [
                entry for entry in filtered_log 
                if datetime.fromisoformat(entry["timestamp"]) >= start_date
            ]
        
        if end_date:
            filtered_log = [
                entry for entry in filtered_log 
                if datetime.fromisoformat(entry["timestamp"]) <= end_date
            ]
        
        return filtered_log


class DataRetentionManager:
    """Manages data retention policies for compliance"""
    
    def __init__(self):
        self.retention_policies = self._initialize_retention_policies()
    
    def _initialize_retention_policies(self) -> Dict[str, Any]:
        """Initialize data retention policies"""
        return {
            "phi_data": {
                "retention_period_years": 7,
                "deletion_method": "secure_wipe",
                "archive_required": True
            },
            "medical_records": {
                "retention_period_years": 10,
                "deletion_method": "secure_wipe",
                "archive_required": True
            },
            "research_data": {
                "retention_period_years": 5,
                "deletion_method": "standard",
                "archive_required": False
            },
            "audit_logs": {
                "retention_period_years": 7,
                "deletion_method": "secure_wipe",
                "archive_required": True
            }
        }
    
    def check_retention_status(self, data_type: str, creation_date: datetime) -> Dict[str, Any]:
        """Check if data should be retained or deleted"""
        policy = self.retention_policies.get(data_type, self.retention_policies["research_data"])
        
        retention_period = timedelta(days=policy["retention_period_years"] * 365)
        expiry_date = creation_date + retention_period
        
        return {
            "data_type": data_type,
            "creation_date": creation_date.isoformat(),
            "expiry_date": expiry_date.isoformat(),
            "days_until_expiry": (expiry_date - datetime.now()).days,
            "should_delete": datetime.now() > expiry_date,
            "deletion_method": policy["deletion_method"],
            "archive_required": policy["archive_required"]
        }


class HIPAAComplianceManager:
    """Main HIPAA compliance manager"""
    
    def __init__(self):
        self.data_classifier = MedicalDataClassifier()
        self.encryption_manager = EncryptionManager()
        self.access_control = AccessControlManager()
        self.audit_logger = AuditLogger()
        self.retention_manager = DataRetentionManager()
    
    def process_medical_data(self, data: Union[str, Dict], user_session: str,
                           operation: str = "read") -> Dict[str, Any]:
        """Process medical data with full compliance checks"""
        try:
            # Validate session
            session = self.access_control.validate_session(user_session)
            if not session:
                self.audit_logger.log_access("unknown", operation, "medical_data", 
                                           "unknown", False, {"reason": "Invalid session"})
                return {"error": "Invalid or expired session", "compliant": False}
            
            # Classify data
            if isinstance(data, str):
                classification = self.data_classifier.classify_content(data)
            else:
                classification = self.data_classifier.classify_content(str(data))
            
            # Check access permissions
            data_type = "phi" if classification["contains_phi"] else "medical_records"
            access_check = self.access_control.check_access_permission(
                user_session, data_type, operation
            )
            
            if not access_check["authorized"]:
                self.audit_logger.log_access(session["user_id"], operation, data_type,
                                           "unknown", False, {"reason": access_check["reason"]})
                return {"error": access_check["reason"], "compliant": False}
            
            # Process data based on classification
            processed_data = data
            if classification["requires_encryption"]:
                if operation in ["write", "store"]:
                    processed_data = self.encryption_manager.encrypt_data(data)
                elif operation == "read" and isinstance(data, dict) and data.get("is_encrypted"):
                    processed_data = self.encryption_manager.decrypt_data(data)
            
            # Log access
            self.audit_logger.log_access(session["user_id"], operation, data_type,
                                       "processed", True, {"classification": classification})
            
            return {
                "processed_data": processed_data,
                "classification": classification,
                "compliant": True,
                "user_id": session["user_id"]
            }
            
        except Exception as e:
            logger.error(f"Error processing medical data: {e}")
            return {"error": str(e), "compliant": False}
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate compliance report"""
        audit_trail = self.audit_logger.get_audit_trail()
        
        # Calculate compliance metrics
        total_accesses = len(audit_trail)
        successful_accesses = len([entry for entry in audit_trail if entry["success"]])
        failed_accesses = total_accesses - successful_accesses
        
        # Get recent activity
        recent_activity = audit_trail[-10:] if audit_trail else []
        
        return {
            "report_generated": datetime.now().isoformat(),
            "compliance_status": "compliant",
            "metrics": {
                "total_data_accesses": total_accesses,
                "successful_accesses": successful_accesses,
                "failed_accesses": failed_accesses,
                "success_rate": successful_accesses / max(total_accesses, 1)
            },
            "recent_activity": recent_activity,
            "active_sessions": len(self.access_control.user_sessions),
            "encryption_status": "enabled",
            "audit_logging": "enabled"
        }


# Global compliance manager instance
compliance_manager = HIPAAComplianceManager()
